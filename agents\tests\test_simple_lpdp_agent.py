"""
Test suite for SimpleLPDPAgent

This test suite validates the functionality of the simplified LPDP agent
including language selection, conversation history, and question generation.
"""

import asyncio
import json
import pytest
from unittest.mock import AsyncMock, patch, MagicMock

# Import the agent
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src', 'voice_agents'))

from terang_agents.agents.simple_lpdp_agent import SimpleLPDPAgent


class TestSimpleLPDPAgent:
    """Test cases for SimpleLPDPAgent"""
    
    def setup_method(self):
        """Setup test fixtures"""
        self.sample_room_metadata = {
            "language": "id",
            "interview_type": "full", 
            "category_name": "LPDP",
            "duration": "01:00:00",
            "name": "LPDP Interview Practice",
            "sections": [],
            "session_resumed": False,
            "resume_context": {}
        }
        
        self.sample_user_metadata = {
            "userName": "<PERSON> Doe",
            "email": "<EMAIL>"
        }
    
    def test_agent_initialization_indonesian(self):
        """Test agent initialization with Indonesian language"""
        room_metadata_json = json.dumps(self.sample_room_metadata)
        
        agent = SimpleLPDPAgent(
            room_metadata=room_metadata_json,
            user_metadata=self.sample_user_metadata
        )
        
        # Verify basic properties
        assert agent.language == "id"
        assert agent.user_name == "John Doe"
        assert agent.first_name == "John"
        assert agent.interview_type == "full"
        assert agent.duration_minutes == 60
        assert agent.estimated_questions >= 3
        assert len(agent.conversation_history) == 0
        assert agent.question_bank is not None
    
    def test_agent_initialization_english(self):
        """Test agent initialization with English language"""
        room_metadata = self.sample_room_metadata.copy()
        room_metadata["language"] = "en"
        room_metadata_json = json.dumps(room_metadata)
        
        agent = SimpleLPDPAgent(
            room_metadata=room_metadata_json,
            user_metadata=self.sample_user_metadata
        )
        
        assert agent.language == "en"
        assert "English" in agent.instructions
    
    def test_agent_initialization_default_language(self):
        """Test agent initialization with default language when not specified"""
        room_metadata = self.sample_room_metadata.copy()
        del room_metadata["language"]  # Remove language key
        room_metadata_json = json.dumps(room_metadata)
        
        agent = SimpleLPDPAgent(
            room_metadata=room_metadata_json,
            user_metadata=self.sample_user_metadata
        )
        
        assert agent.language == "en"  # Should default to English
    
    def test_session_resume_configuration(self):
        """Test session resume configuration"""
        room_metadata = self.sample_room_metadata.copy()
        room_metadata["session_resumed"] = True
        room_metadata["resume_context"] = {"previous_questions": 3}
        room_metadata_json = json.dumps(room_metadata)
        
        agent = SimpleLPDPAgent(
            room_metadata=room_metadata_json,
            user_metadata=self.sample_user_metadata
        )
        
        assert agent.is_existing_session == True
        assert "RESUMED SESSION" in agent.instructions
        assert "CRITICAL - SESSION RESUME MODE" in agent.instructions or "PENTING - MODE LANJUTAN SESI" in agent.instructions
    
    def test_question_bank_integration(self):
        """Test question bank integration"""
        room_metadata_json = json.dumps(self.sample_room_metadata)
        
        agent = SimpleLPDPAgent(
            room_metadata=room_metadata_json,
            user_metadata=self.sample_user_metadata
        )
        
        # Verify question bank is loaded
        assert isinstance(agent.question_bank, dict)
        assert len(agent.question_bank) > 0
        
        # Verify categories are included in instructions
        for category in agent.question_bank.keys():
            assert category.upper() in agent.instructions
    
    def test_conversation_history_formatting(self):
        """Test conversation history formatting"""
        room_metadata_json = json.dumps(self.sample_room_metadata)
        
        agent = SimpleLPDPAgent(
            room_metadata=room_metadata_json,
            user_metadata=self.sample_user_metadata
        )
        
        # Test empty history
        formatted = agent._format_conversation_history()
        assert "Belum ada percakapan" in formatted or "No previous conversation" in formatted
        
        # Add some conversation history
        agent.conversation_history = [
            {"role": "assistant", "content": "Pertanyaan pertama"},
            {"role": "user", "content": "Jawaban kandidat"},
            {"role": "assistant", "content": "Pertanyaan kedua"}
        ]
        
        formatted = agent._format_conversation_history()
        assert "Interviewer:" in formatted
        assert "Candidate:" in formatted
        assert "Pertanyaan pertama" in formatted
    
    @patch('terang_agents.configs.llm_utils.generate_ai_response')
    async def test_get_next_question(self, mock_generate_ai_response):
        """Test get_next_question function"""
        mock_generate_ai_response.return_value = "Ceritakan tentang latar belakang pendidikan Anda?"
        
        room_metadata_json = json.dumps(self.sample_room_metadata)
        agent = SimpleLPDPAgent(
            room_metadata=room_metadata_json,
            user_metadata=self.sample_user_metadata
        )
        
        # Mock context
        mock_context = MagicMock()
        
        # Test first question (no user response)
        question = await agent.get_next_question(mock_context, "")
        
        assert question == "Ceritakan tentang latar belakang pendidikan Anda?"
        assert len(agent.conversation_history) == 1
        assert agent.conversation_history[0]["role"] == "assistant"
        
        # Test follow-up question with user response
        user_response = "Saya lulusan teknik informatika dari ITB"
        question2 = await agent.get_next_question(mock_context, user_response)
        
        assert len(agent.conversation_history) == 3  # user response + new question
        assert agent.conversation_history[1]["role"] == "user"
        assert agent.conversation_history[1]["content"] == user_response
    
    @patch('terang_agents.configs.llm_utils.generate_ai_response')
    async def test_provide_feedback(self, mock_generate_ai_response):
        """Test provide_feedback function"""
        mock_generate_ai_response.return_value = "Jawaban Anda menunjukkan pemahaman yang baik tentang motivasi studi."
        
        room_metadata_json = json.dumps(self.sample_room_metadata)
        agent = SimpleLPDPAgent(
            room_metadata=room_metadata_json,
            user_metadata=self.sample_user_metadata
        )
        
        mock_context = MagicMock()
        answer = "Saya ingin melanjutkan studi untuk mengembangkan kemampuan di bidang AI"
        
        feedback = await agent.provide_feedback(mock_context, answer)
        
        assert feedback == "Jawaban Anda menunjukkan pemahaman yang baik tentang motivasi studi."
        mock_generate_ai_response.assert_called_once()
        
        # Verify the prompt contains the answer
        call_args = mock_generate_ai_response.call_args[0][0]
        assert answer in call_args
    
    @patch('terang_agents.configs.llm_utils.generate_ai_response')
    async def test_conclude_interview(self, mock_generate_ai_response):
        """Test conclude_interview function"""
        mock_generate_ai_response.return_value = "Secara keseluruhan, Anda menunjukkan persiapan yang baik untuk wawancara LPDP."
        
        room_metadata_json = json.dumps(self.sample_room_metadata)
        agent = SimpleLPDPAgent(
            room_metadata=room_metadata_json,
            user_metadata=self.sample_user_metadata
        )
        
        # Add some conversation history
        agent.conversation_history = [
            {"role": "assistant", "content": "Pertanyaan 1"},
            {"role": "user", "content": "Jawaban 1"},
            {"role": "assistant", "content": "Pertanyaan 2"},
            {"role": "user", "content": "Jawaban 2"}
        ]
        
        mock_context = MagicMock()
        conclusion = await agent.conclude_interview(mock_context)
        
        assert conclusion == "Secara keseluruhan, Anda menunjukkan persiapan yang baik untuk wawancara LPDP."
        mock_generate_ai_response.assert_called_once()
        
        # Verify the prompt contains conversation history
        call_args = mock_generate_ai_response.call_args[0][0]
        assert "Pertanyaan 1" in call_args
        assert "Jawaban 1" in call_args
    
    def test_duration_and_question_estimation(self):
        """Test duration parsing and question estimation"""
        test_cases = [
            ("00:30:00", 30, 7),   # 30 minutes -> ~7 questions
            ("01:00:00", 60, 15),  # 60 minutes -> 15 questions  
            ("01:30:00", 90, 15),  # 90 minutes -> 15 questions (capped)
            ("00:15:00", 15, 3),   # 15 minutes -> 3 questions (minimum)
        ]
        
        for duration_str, expected_minutes, expected_questions in test_cases:
            room_metadata = self.sample_room_metadata.copy()
            room_metadata["duration"] = duration_str
            room_metadata_json = json.dumps(room_metadata)
            
            agent = SimpleLPDPAgent(
                room_metadata=room_metadata_json,
                user_metadata=self.sample_user_metadata
            )
            
            assert agent.duration_minutes == expected_minutes
            assert agent.estimated_questions == expected_questions
    
    def test_invalid_room_metadata(self):
        """Test handling of invalid room metadata"""
        # Test with invalid JSON
        agent = SimpleLPDPAgent(
            room_metadata="invalid json",
            user_metadata=self.sample_user_metadata
        )
        
        # Should use defaults
        assert agent.language == "en"
        assert agent.interview_type == "full"
        assert agent.room_config == {}
    
    def test_missing_user_metadata(self):
        """Test handling of missing user metadata"""
        room_metadata_json = json.dumps(self.sample_room_metadata)
        
        agent = SimpleLPDPAgent(
            room_metadata=room_metadata_json,
            user_metadata={}
        )
        
        assert agent.user_name == "Anonymous User"
        assert agent.first_name == "Anonymous User"


# Integration test
class TestSimpleLPDPAgentIntegration:
    """Integration tests for SimpleLPDPAgent"""
    
    @pytest.mark.asyncio
    async def test_full_conversation_flow(self):
        """Test a complete conversation flow"""
        room_metadata = {
            "language": "id",
            "interview_type": "full",
            "duration": "00:30:00"
        }
        user_metadata = {"userName": "Test User"}
        
        with patch('terang_agents.configs.llm_utils.generate_ai_response') as mock_ai:
            # Mock AI responses
            mock_ai.side_effect = [
                "Ceritakan tentang latar belakang Anda?",
                "Apa motivasi Anda untuk melanjutkan studi?", 
                "Jawaban Anda menunjukkan motivasi yang kuat.",
                "Secara keseluruhan, Anda menunjukkan persiapan yang baik."
            ]
            
            agent = SimpleLPDPAgent(
                room_metadata=json.dumps(room_metadata),
                user_metadata=user_metadata
            )
            
            mock_context = MagicMock()
            
            # Start conversation
            q1 = await agent.get_next_question(mock_context, "")
            assert q1 == "Ceritakan tentang latar belakang Anda?"
            
            # Continue conversation
            q2 = await agent.get_next_question(mock_context, "Saya lulusan teknik")
            assert q2 == "Apa motivasi Anda untuk melanjutkan studi?"
            
            # Provide feedback
            feedback = await agent.provide_feedback(mock_context, "Saya ingin berkontribusi")
            assert "motivasi yang kuat" in feedback
            
            # Conclude
            conclusion = await agent.conclude_interview(mock_context)
            assert "persiapan yang baik" in conclusion
            
            # Verify conversation history
            assert len(agent.conversation_history) == 4


if __name__ == "__main__":
    # Run basic tests
    print("Running SimpleLPDPAgent tests...")
    
    # Test initialization
    test_agent = TestSimpleLPDPAgent()
    test_agent.setup_method()
    
    try:
        test_agent.test_agent_initialization_indonesian()
        print("✓ Indonesian initialization test passed")
        
        test_agent.test_agent_initialization_english()
        print("✓ English initialization test passed")
        
        test_agent.test_question_bank_integration()
        print("✓ Question bank integration test passed")
        
        test_agent.test_conversation_history_formatting()
        print("✓ Conversation history formatting test passed")
        
        print("\n✅ All basic tests passed!")
        print("\nTo run async tests, use: pytest test_simple_lpdp_agent.py")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        raise
