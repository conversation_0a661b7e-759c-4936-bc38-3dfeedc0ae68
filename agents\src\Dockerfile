# syntax=docker/dockerfile:1
ARG PYTHON_VERSION=3.11.6
FROM python:${PYTHON_VERSION}-slim

# Prevents Python from writing pyc files.
ENV PYTHONDONTWRITEBYTECODE=1

# Keeps <PERSON> from buffering stdout and stderr to avoid situations where
# the application crashes without emitting any logs due to buffering.
ENV PYTHONUNBUFFERED=1

# Create a non-privileged user that the app will run under.
ARG UID=10001
RUN adduser \
    --disabled-password \
    --gecos "" \
    --home "/home/<USER>" \
    --shell "/sbin/nologin" \
    --uid "${UID}" \
    appuser

# Install necessary system dependencies
RUN apt-get update && \
    apt-get install -y \
    gcc \
    g++ \
    python3-dev \
    ffmpeg \
    libsndfile1 \
    && rm -rf /var/lib/apt/lists/*

# Create and prepare transcript directory
RUN mkdir -p /home/<USER>/transcripts && \
    chown -R appuser:appuser /home/<USER>/transcripts

USER appuser

RUN mkdir -p /home/<USER>/.cache
RUN chown -R appuser /home/<USER>/.cache

WORKDIR /home/<USER>

COPY --chown=appuser:appuser voice_agents/requirements.txt .
RUN python -m pip install --user --no-cache-dir -r requirements.txt

# Copy the application code
COPY --chown=appuser:appuser voice_agents .
COPY --chown=appuser:appuser .env .env

# Download required model files during build
RUN python basic_agent.py download-files

# Run the application
ENTRYPOINT ["python", "basic_agent.py"]
CMD ["start"]