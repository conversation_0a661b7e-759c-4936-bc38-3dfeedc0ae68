# Simple LPDP Agent Documentation

## Overview

`SimpleLPDPAgent` adalah implementasi yang disederhanakan dari LPDP interview agent yang menggunakan conversation history approach dengan Gemini AI. Agent ini menggantikan kompleksitas question bank tracking dengan pendekatan yang lebih natural dan fleksibel.

## Key Features

### 1. Conversation History Approach
- Menggunakan Gemini conversation history untuk menjaga konteks
- Tidak ada tracking kompleks untuk pertanyaan yang sudah ditanyakan
- Pertanyaan mengalir secara natural berdasarkan respons kandidat

### 2. Comprehensive System Prompt
- System prompt yang detail mencakup semua kategori LPDP
- Question bank digunakan sebagai referensi, bukan rigid tracking
- Instruksi yang jelas untuk gaya wawancara yang natural

### 3. Multi-language Support
- Mendukung Bahasa Indonesia (`'id'`) dan English (`'en'`)
- Language selection berdasarkan `room_config.get('language', 'en')`
- System prompt dan responses disesuaikan dengan bahasa yang dipilih

### 4. Simplified Architecture
- Hanya 3 function tools: `get_next_question`, `provide_feedback`, `conclude_interview`
- Tidak ada kompleksitas category tracking atau asked questions management
- Fokus pada conversation flow yang natural

## Architecture Comparison

### Complex Agent (lpdp_agent.py)
```python
# Complex tracking
self.session_state = {
    "asked_questions": set(),
    "questions_asked": 0,
    "last_category": None,
    "category_distribution": {},
    # ... many more state variables
}

# Multiple function tools
@function_tool
async def start_interview()
@function_tool  
async def ask_question()
@function_tool
async def continue_resume_session()
@function_tool
async def analyze_answer_depth()
# ... 8+ function tools total
```

### Simple Agent (simple_lpdp_agent.py)
```python
# Simple conversation history
self.conversation_history = []

# Only 3 essential function tools
@function_tool
async def get_next_question()
@function_tool
async def provide_feedback()
@function_tool
async def conclude_interview()
```

## Configuration

### Room Metadata
Agent menggunakan room metadata yang sama dengan agent kompleks:

```json
{
  "language": "id",
  "interview_type": "full",
  "category_name": "LPDP", 
  "duration": "01:00:00",
  "name": "LPDP Interview Practice",
  "sections": [...],
  "session_resumed": false,
  "resume_context": {...}
}
```

### Language Selection
```python
self.language = self.room_config.get('language', 'en')

# Bahasa Indonesia
if self.language == 'id':
    language_name = "Bahasa Indonesia"
    
# English  
else:
    language_name = "English"
```

## Function Tools

### 1. get_next_question(user_response: str)
Generates the next interview question based on conversation history.

**Features:**
- Adds user response to conversation history
- Uses Gemini to generate contextual next question
- Considers interview duration and estimated questions
- Maintains natural conversation flow

**Example Usage:**
```python
next_question = await agent.get_next_question(
    context=context,
    user_response="Saya ingin melanjutkan studi S2 di bidang AI..."
)
```

### 2. provide_feedback(answer: str)
Provides constructive feedback on candidate's answer.

**Features:**
- Analyzes answer quality and depth
- Provides encouraging and constructive feedback
- Focuses on strengths and improvement areas
- Language-appropriate responses

### 3. conclude_interview()
Provides comprehensive interview conclusion and recommendations.

**Features:**
- Summarizes overall performance
- Highlights strengths and weaknesses
- Gives concrete preparation advice
- Motivational closing

## Question Bank Integration

Question bank tetap digunakan sebagai referensi dalam system prompt:

```python
# Get question bank for reference (not rigid tracking)
self.question_bank = get_questions_for_interview_type(
    self.interview_type, 
    self.category_name, 
    self.sections, 
    self.language
)

# Include examples in system prompt
for category, examples in question_examples.items():
    category_focus = get_category_focus(category, self.language)
    system_prompt += f"""
    {category.upper()}:
    Focus: {category_focus}
    Example questions:
    """
```

## Benefits

### 1. Maintainability
- Kode lebih sederhana dan mudah dipahami
- Tidak ada state management yang kompleks
- Debugging lebih mudah

### 2. Flexibility
- Pertanyaan mengalir natural berdasarkan konteks
- Tidak terikat pada urutan pertanyaan yang kaku
- AI dapat menyesuaikan dengan respons kandidat

### 3. Performance
- Lebih sedikit function calls
- Tidak ada overhead tracking kompleks
- Response time lebih cepat

### 4. Natural Conversation
- Menggunakan kekuatan Gemini conversation history
- Pertanyaan follow-up yang relevan
- Gaya wawancara yang lebih manusiawi

## Usage Example

```python
from terang_agents.agents import SimpleLPDPAgent

# Initialize agent
agent = SimpleLPDPAgent(
    room_metadata=json.dumps({
        "language": "id",
        "interview_type": "full",
        "duration": "01:00:00"
    }),
    user_metadata={"userName": "John Doe"}
)

# Start conversation
first_question = await agent.get_next_question(context, "")

# Continue conversation
next_question = await agent.get_next_question(
    context, 
    "Saya tertarik dengan AI karena..."
)

# Provide feedback
feedback = await agent.provide_feedback(
    context,
    "Jawaban kandidat tentang motivasi studi..."
)

# Conclude interview
conclusion = await agent.conclude_interview(context)
```

## Migration from Complex Agent

Untuk migrasi dari `lpdp_agent.py` ke `simple_lpdp_agent.py`:

1. **Import Change:**
```python
# Old
from terang_agents.agents.lpdp_agent import LPDPInterviewAgent

# New  
from terang_agents.agents.simple_lpdp_agent import SimpleLPDPAgent
```

2. **Initialization:** Sama, tidak ada perubahan
3. **Function Calls:** Gunakan 3 function tools yang tersedia
4. **Configuration:** Room metadata format tetap sama

## Testing

Untuk testing agent baru:

```python
# Test basic initialization
agent = SimpleLPDPAgent(room_metadata, user_metadata)

# Test question generation
question = await agent.get_next_question(context, "")
assert question is not None
assert len(question) > 0

# Test language selection
assert agent.language in ['id', 'en']

# Test conversation history
agent.conversation_history.append({"role": "user", "content": "test"})
assert len(agent.conversation_history) == 1
```
