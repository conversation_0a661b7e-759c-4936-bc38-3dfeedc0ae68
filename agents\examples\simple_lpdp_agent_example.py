"""
Example usage of SimpleLPDPAgent

This example demonstrates how to use the SimpleLPDPAgent for LPDP interview practice
with both Indonesian and English language support.
"""

import asyncio
import json
import sys
import os

# Add the source directory to Python path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src', 'voice_agents'))

from terang_agents.agents.simple_lpdp_agent import SimpleLPDPAgent
from unittest.mock import MagicMock


async def demo_indonesian_interview():
    """Demonstrate Indonesian language interview"""
    print("=== Demo: LPDP Interview dalam Bahasa Indonesia ===\n")
    
    # Setup room metadata for Indonesian interview
    room_metadata = {
        "language": "id",
        "interview_type": "full",
        "category_name": "LPDP",
        "duration": "01:00:00",
        "name": "<PERSON><PERSON>han Wawancara LPDP",
        "sections": [],
        "session_resumed": False,
        "resume_context": {}
    }
    
    user_metadata = {
        "userName": "<PERSON><PERSON>",
        "email": "<EMAIL>"
    }
    
    # Initialize agent
    agent = SimpleLPDPAgent(
        room_metadata=json.dumps(room_metadata),
        user_metadata=user_metadata
    )
    
    print(f"Agent initialized:")
    print(f"- Language: {agent.language}")
    print(f"- User: {agent.user_name}")
    print(f"- Duration: {agent.duration_minutes} minutes")
    print(f"- Estimated questions: {agent.estimated_questions}")
    print(f"- Question categories: {list(agent.question_bank.keys())}")
    print()
    
    # Mock context for function calls
    mock_context = MagicMock()
    
    # Simulate conversation flow
    print("=== Conversation Flow ===")
    
    # Note: In real usage, these would call actual Gemini API
    # For demo, we'll show the structure and prompts
    
    print("1. Starting interview...")
    print("   System prompt includes:")
    print("   - Comprehensive LPDP interview guidelines in Indonesian")
    print("   - Question bank examples for all categories")
    print("   - Natural conversation flow instructions")
    print()
    
    print("2. Question generation process:")
    print("   - Agent uses conversation history with Gemini")
    print("   - Considers interview duration and progress")
    print("   - Adapts to candidate responses")
    print()
    
    # Show system prompt structure (first 500 chars)
    system_prompt_preview = agent.instructions[:500] + "..."
    print("3. System prompt preview:")
    print(f"   {system_prompt_preview}")
    print()
    
    print("4. Conversation history tracking:")
    print(f"   Current history length: {len(agent.conversation_history)}")
    print("   Format: [{'role': 'user/assistant', 'content': '...'}]")
    print()


async def demo_english_interview():
    """Demonstrate English language interview"""
    print("=== Demo: LPDP Interview in English ===\n")
    
    # Setup room metadata for English interview
    room_metadata = {
        "language": "en",
        "interview_type": "partial",
        "category_name": "Personal Background",
        "duration": "00:30:00",
        "name": "LPDP Interview Practice",
        "sections": [
            {"id": "personal", "name": "Personal Background"},
            {"id": "study_plans", "name": "Study Plans"}
        ],
        "session_resumed": False
    }
    
    user_metadata = {
        "userName": "Jane Smith",
        "email": "<EMAIL>"
    }
    
    # Initialize agent
    agent = SimpleLPDPAgent(
        room_metadata=json.dumps(room_metadata),
        user_metadata=user_metadata
    )
    
    print(f"Agent initialized:")
    print(f"- Language: {agent.language}")
    print(f"- User: {agent.first_name}")
    print(f"- Interview type: {agent.interview_type}")
    print(f"- Duration: {agent.duration_minutes} minutes")
    print(f"- Available categories: {list(agent.question_bank.keys())}")
    print()
    
    # Show difference in system prompt for English
    if "English" in agent.instructions:
        print("✓ System prompt configured for English language")
    if "friendly and professional" in agent.instructions:
        print("✓ English tone guidelines included")
    print()


async def demo_session_resume():
    """Demonstrate session resume functionality"""
    print("=== Demo: Session Resume ===\n")
    
    room_metadata = {
        "language": "id",
        "interview_type": "full",
        "duration": "01:00:00",
        "session_resumed": True,
        "resume_context": {
            "previous_questions": 3,
            "last_category": "personal",
            "elapsed_time": 15
        }
    }
    
    user_metadata = {"userName": "Ahmad Rahman"}
    
    agent = SimpleLPDPAgent(
        room_metadata=json.dumps(room_metadata),
        user_metadata=user_metadata
    )
    
    print(f"Resume session initialized:")
    print(f"- Session resumed: {agent.is_existing_session}")
    print(f"- Resume context: {agent.resume_context}")
    print()
    
    if agent.is_existing_session:
        if "PENTING - MODE LANJUTAN SESI" in agent.instructions:
            print("✓ Indonesian resume instructions included")
        elif "CRITICAL - SESSION RESUME MODE" in agent.instructions:
            print("✓ English resume instructions included")
    print()


async def demo_function_tools():
    """Demonstrate function tool usage"""
    print("=== Demo: Function Tools ===\n")
    
    room_metadata = {
        "language": "id",
        "interview_type": "full",
        "duration": "00:45:00"
    }
    
    user_metadata = {"userName": "Test User"}
    
    agent = SimpleLPDPAgent(
        room_metadata=json.dumps(room_metadata),
        user_metadata=user_metadata
    )
    
    mock_context = MagicMock()
    
    print("Available function tools:")
    print("1. get_next_question(user_response: str)")
    print("2. provide_feedback(answer: str)")
    print("3. conclude_interview()")
    print()
    
    # Simulate adding to conversation history
    print("Simulating conversation history:")
    agent.conversation_history = [
        {"role": "assistant", "content": "Ceritakan tentang latar belakang Anda?"},
        {"role": "user", "content": "Saya lulusan teknik informatika dari ITB"},
        {"role": "assistant", "content": "Apa motivasi Anda untuk melanjutkan studi?"},
        {"role": "user", "content": "Saya ingin mengembangkan kemampuan di bidang AI"}
    ]
    
    print(f"Conversation history length: {len(agent.conversation_history)}")
    print()
    
    # Show conversation formatting
    formatted_history = agent._format_conversation_history()
    print("Formatted conversation history:")
    print(formatted_history)
    print()


def demo_configuration_options():
    """Demonstrate various configuration options"""
    print("=== Demo: Configuration Options ===\n")
    
    configurations = [
        {
            "name": "Full Interview - Indonesian",
            "config": {
                "language": "id",
                "interview_type": "full",
                "duration": "01:00:00",
                "category_name": "LPDP"
            }
        },
        {
            "name": "Partial Interview - English",
            "config": {
                "language": "en", 
                "interview_type": "partial",
                "duration": "00:30:00",
                "category_name": "Personal Background"
            }
        },
        {
            "name": "Short Practice - Indonesian",
            "config": {
                "language": "id",
                "interview_type": "full",
                "duration": "00:15:00"
            }
        }
    ]
    
    for config_demo in configurations:
        print(f"{config_demo['name']}:")
        
        agent = SimpleLPDPAgent(
            room_metadata=json.dumps(config_demo['config']),
            user_metadata={"userName": "Demo User"}
        )
        
        print(f"  - Language: {agent.language}")
        print(f"  - Duration: {agent.duration_minutes} minutes")
        print(f"  - Estimated questions: {agent.estimated_questions}")
        print(f"  - Categories: {len(agent.question_bank)} available")
        print()


async def main():
    """Run all demos"""
    print("SimpleLPDPAgent Demo\n")
    print("=" * 50)
    
    try:
        await demo_indonesian_interview()
        print("\n" + "=" * 50)
        
        await demo_english_interview()
        print("\n" + "=" * 50)
        
        await demo_session_resume()
        print("\n" + "=" * 50)
        
        await demo_function_tools()
        print("\n" + "=" * 50)
        
        demo_configuration_options()
        print("\n" + "=" * 50)
        
        print("✅ All demos completed successfully!")
        print("\nKey Benefits of SimpleLPDPAgent:")
        print("- Natural conversation flow using Gemini history")
        print("- Simplified architecture with only 3 function tools")
        print("- Multi-language support (Indonesian/English)")
        print("- Question bank integration without rigid tracking")
        print("- Easy configuration through room metadata")
        
    except Exception as e:
        print(f"❌ Demo failed: {e}")
        raise


if __name__ == "__main__":
    asyncio.run(main())
