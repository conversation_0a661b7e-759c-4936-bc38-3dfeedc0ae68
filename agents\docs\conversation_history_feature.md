# Conversation History Feature - Men<PERSON>gah Pertanyaan Duplikat

## Overview

Fitur conversation history telah diimplementasikan pada `agent.py` untuk mencegah AI menanyakan pertanyaan yang sama dalam satu sesi interview. Fitur ini memastikan bahwa setiap pertanyaan yang diajukan dalam sesi interview adalah unik dan tidak berulang.

## Implementasi

### 1. Tracking Pertanyaan yang Sudah Ditanyakan

#### `_extract_asked_questions_from_transcript()`
- Mengekstrak semua pertanyaan yang sudah ditanyakan dari conversation history (GLOBAL_TRANSCRIPT)
- Memfilter entry interviewer dan mengidentifikasi pertanyaan berdasarkan:
  - Pertanyaan yang diakhiri dengan tanda tanya (?)
  - Pertanyaan yang mengandung indikator pertanyaan seperti "why did you choose", "how will you contribute", dll.
- Menormalisasi pertanyaan untuk perbandingan yang konsisten

#### `_normalize_question_for_comparison()`
- Menormalisasi teks pertanyaan untuk perbandingan yang akurat
- Menghapus prefix kontekstual yang bervariasi
- Menormalisasi spasi dan tanda baca
- Mengkonversi ke lowercase untuk perbandingan case-insensitive

### 2. Session State Enhancement

Session state sekarang menyimpan:
```python
"asked_questions": set()  # Set berisi pertanyaan yang sudah dinormalisasi
```

### 3. Pemilihan Pertanyaan yang Belum Ditanyakan

#### `_get_unasked_question_from_category()`
- Memilih pertanyaan dari kategori tertentu yang belum pernah ditanyakan
- Membandingkan dengan set `asked_questions` untuk menghindari duplikasi
- Menambahkan pertanyaan yang dipilih ke dalam set `asked_questions`
- Fallback ke pertanyaan random jika semua pertanyaan dalam kategori sudah ditanyakan

### 4. Update pada Function Tools

#### `ask_first_question()`
- Menggunakan `_get_unasked_question_from_category()` untuk memilih pertanyaan pertama
- Memastikan pertanyaan pertama tidak duplikat dengan pertanyaan sebelumnya (untuk resumed session)

#### `get_next_question()`
- Prioritas pertama: mencari pertanyaan yang belum ditanyakan dari question bank
- Fallback: menggunakan AI generation dengan konteks pertanyaan yang sudah ditanyakan
- Menambahkan pertanyaan yang dipilih/digenerate ke dalam `asked_questions`

#### `continue_resume_session()`
- Sama seperti `get_next_question()` tetapi dengan konteks khusus untuk resumed session
- Mempertimbangkan pertanyaan yang sudah ditanyakan di sesi sebelumnya

## Fitur Utama

### 1. Deteksi Pertanyaan Duplikat
- Sistem menormalisasi pertanyaan untuk mendeteksi kesamaan meskipun ada variasi kecil dalam frasa
- Mengabaikan prefix kontekstual yang bervariasi seperti "Building on what you shared," atau "Berdasarkan apa yang Anda sampaikan,"

### 2. Multi-Language Support
- Mendukung deteksi pertanyaan dalam Bahasa Indonesia dan English
- Indikator pertanyaan disesuaikan dengan kedua bahasa

### 3. Graceful Fallback
- Jika semua pertanyaan dalam question bank sudah ditanyakan, sistem akan:
  1. Menggunakan AI generation dengan konteks pertanyaan yang sudah ditanyakan
  2. Fallback ke pertanyaan random jika diperlukan
  3. Memastikan interview tetap berjalan lancar

### 4. Resume Session Support
- Pertanyaan yang sudah ditanyakan di sesi sebelumnya akan diingat
- Resumed session akan melanjutkan dengan pertanyaan yang belum ditanyakan

## Contoh Penggunaan

### Skenario 1: New Session
```python
# Pertanyaan pertama
"Tell me about yourself and your background."

# Pertanyaan kedua (akan menghindari pertanyaan serupa)
"Why did you choose this specific program and university?"

# Sistem akan memastikan tidak ada duplikasi
```

### Skenario 2: Resumed Session
```python
# Session sebelumnya sudah menanyakan:
# - "Tell me about yourself"
# - "Why did you choose this program"

# Resumed session akan melanjutkan dengan pertanyaan yang belum ditanyakan:
"How will you contribute to Indonesia after completing your studies?"
```

## Logging dan Monitoring

- Sistem mencatat jumlah pertanyaan yang sudah ditanyakan
- Log informasi tentang pemilihan pertanyaan dari kategori tertentu
- Warning jika semua pertanyaan dalam kategori sudah ditanyakan

## Benefits

1. **Improved User Experience**: Tidak ada pertanyaan yang berulang dalam satu sesi
2. **Better Interview Flow**: Pertanyaan yang lebih beragam dan komprehensif
3. **Efficient Time Usage**: Waktu interview digunakan secara optimal tanpa redundansi
4. **Consistent Quality**: Kualitas interview tetap terjaga meskipun ada session resume

## Technical Notes

- Menggunakan Python `set()` untuk O(1) lookup performance
- Normalisasi pertanyaan menggunakan regex untuk konsistensi
- Thread-safe karena menggunakan session state yang terisolasi per user
- Memory efficient dengan menyimpan hanya normalized question strings
