import asyncio
import json
import random
from datetime import datetime

from livekit.agents import Agent, RunContext
from livekit.agents.llm import function_tool

from ..configs.config import logger
from ..configs.llm_utils import generate_ai_response
from ..questions.lpdp_question_bank import get_questions_for_interview_type, get_category_focus
from ..transcript_manager import TranscriptManager, GLOBAL_TRANSCRIPT
from ..helper_functions.utils import (
    parse_duration_to_minutes,
    format_duration_for_speech,
    translate_section_names,
)


class SimpleLPDPAgent(Agent):
    def __init__(self, room_metadata=None, user_metadata=None) -> None:
        global GLOBAL_TRANSCRIPT
        
        # Parse room metadata
        self.room_config = {}
        if room_metadata:
            try:
                self.room_config = json.loads(room_metadata)
                logger.info(f"Parsed room metadata: {self.room_config}")
            except json.JSONDecodeError as e:
                logger.error(f"Failed to parse room metadata: {e}")
                self.room_config = {}
        
        self.user_metadata = user_metadata
        self.language = self.room_config.get('language', 'en')
        
        # Extract session information
        self.is_existing_session = self.room_config.get('session_resumed', False)
        self.resume_context = self.room_config.get('resume_context', {})
        
        # Initialize conversation history
        self.conversation_history = []
        
        # Prepare session configuration
        self._prepare_session_config()
        
        # Generate comprehensive system prompt
        system_prompt = self._generate_system_prompt()
        
        # Initialize agent with system prompt
        super().__init__(instructions=system_prompt)

        # Flag to track if introduction has been given
        self.introduction_given = False

    async def on_enter(self):
        """Called when agent enters the session - provide introduction."""
        if not self.is_existing_session and not self.introduction_given:
            # For new sessions, give introduction automatically
            await self.session.generate_reply(
                instructions="Use the start_interview function tool to provide a proper introduction."
            )

    def _prepare_session_config(self):
        """Prepare session configuration from room metadata."""
        self.user_name = self.user_metadata.get('userName', 'Anonymous User')
        self.first_name = self.user_name.split()[0] if self.user_name != "Anonymous User" else self.user_name
        
        # Interview configuration
        self.interview_type = self.room_config.get('interview_type', 'full')
        self.category_name = self.room_config.get('category_name', 'LPDP')
        self.sections = self.room_config.get('sections', [])
        self.interview_name = self.room_config.get('name', 'LPDP Interview')
        
        # Duration configuration
        duration_str = self.room_config.get('duration', '01:00:00')
        self.duration_minutes = parse_duration_to_minutes(duration_str)
        self.estimated_questions = max(3, min(15, self.duration_minutes // 4))
        
        # Get question bank for reference (not for rigid tracking)
        self.question_bank = get_questions_for_interview_type(
            self.interview_type, 
            self.category_name, 
            self.sections, 
            self.language
        )
        
        logger.info(f"Simple LPDP Agent initialized - Language: {self.language}, Duration: {self.duration_minutes}min, Questions: {self.estimated_questions}")

    def _generate_system_prompt(self) -> str:
        """Generate comprehensive system prompt for conversation history approach."""
        
        # Language-specific configurations
        if self.language == 'id':
            language_name = "Bahasa Indonesia"
            welcome_style = "ramah dan profesional"
            interview_context = "wawancara beasiswa LPDP"
            categories_text = "kategori pertanyaan"
        else:
            language_name = "English"
            welcome_style = "friendly and professional"
            interview_context = "LPDP scholarship interview"
            categories_text = "question categories"
        
        # Build question bank reference for system prompt
        question_categories = list(self.question_bank.keys())
        question_examples = {}
        for category, questions in self.question_bank.items():
            # Take first 3 questions as examples
            question_examples[category] = questions[:3]
        
        # Session instruction based on type
        session_instruction = ""
        if self.is_existing_session:
            if self.language == 'id':
                session_instruction = """
                PENTING - MODE LANJUTAN SESI:
                Ini adalah sesi yang DILANJUTKAN untuk {self.first_name}.
                - JANGAN berikan pesan selamat datang lagi
                - JANGAN minta perkenalan lagi
                - LANGSUNG lanjutkan dari konteks sebelumnya
                - Gunakan informasi dari resume_context jika tersedia
                """
            else:
                session_instruction = """
                CRITICAL - SESSION RESUME MODE:
                This is a RESUMED session for {self.first_name}.
                - NEVER give a welcome message again
                - NEVER ask for an introduction again
                - IMMEDIATELY continue from previous context
                - Use information from resume_context if available
                """
        else:
            if self.language == 'id':
                session_instruction = """
                INSTRUKSI SESI BARU:
                Ini adalah sesi BARU untuk {self.first_name}.
                - SELALU mulai dengan menggunakan function tool start_interview() untuk memberikan introduction
                - Setelah introduction, lanjutkan dengan pertanyaan pertama menggunakan get_next_question()
                - Pastikan kandidat merasa welcome dan siap untuk memulai
                """
            else:
                session_instruction = """
                NEW SESSION INSTRUCTIONS:
                This is a NEW session for {self.first_name}.
                - ALWAYS start by using the start_interview() function tool to provide introduction
                - After introduction, continue with the first question using get_next_question()
                - Ensure the candidate feels welcome and ready to begin
                """
        
        system_prompt = f"""
        You are Terra, an expert LPDP (Indonesia Endowment Fund for Education) interviewer with over 20 years of experience. You conduct {interview_context} practice sessions to help candidates prepare for their actual interviews.

        INTERVIEW CONFIGURATION:
        - Candidate Name: {self.user_name} (Call them {self.first_name})
        - Interview Name: {self.interview_name}
        - Duration: {self.duration_minutes} minutes
        - Type: {self.interview_type}
        - Category Focus: {self.category_name}
        - Estimated Questions: {self.estimated_questions}
        - Language: {language_name}
        - Session Type: {"RESUMED SESSION" if self.is_existing_session else "NEW SESSION"}

        {session_instruction}

        FUNCTION TOOLS AVAILABLE:
        1. start_interview() - Use this to provide introduction for new sessions
        2. get_next_question(user_response) - Generate contextual questions based on conversation history
        3. provide_feedback(answer) - Give constructive feedback on candidate responses
        4. conclude_interview() - Provide final summary and recommendations

        LANGUAGE RULES:
        - Conduct the entire interview in {language_name}
        - Maintain a {welcome_style} tone throughout
        - Use natural, conversational language
        - Adapt your speaking style to be encouraging yet professional

        QUESTION CATEGORIES AND EXAMPLES:
        You have access to these {categories_text}:
        """
        
        # Add question examples to system prompt
        for category, examples in question_examples.items():
            category_focus = get_category_focus(category, self.language)
            system_prompt += f"""
        
        {category.upper()}:
        Focus: {category_focus}
        Example questions:
        """
            for i, example in enumerate(examples, 1):
                system_prompt += f"        {i}. {example}\n"
        
        # Continue with conversation guidelines
        if self.language == 'id':
            conversation_guidelines = """
        
        PANDUAN PERCAKAPAN:
        - Gunakan pendekatan percakapan yang natural dan mengalir
        - Ajukan pertanyaan follow-up berdasarkan jawaban kandidat
        - Berikan feedback konstruktif dan dorongan
        - Sesuaikan tingkat kesulitan berdasarkan respons kandidat
        - Pastikan semua kategori utama tercover dalam durasi yang tersedia
        - Buat pertanyaan yang relevan dengan konteks jawaban sebelumnya
        - Jangan terlalu kaku mengikuti urutan pertanyaan yang sudah ditetapkan
        
        GAYA WAWANCARA:
        - Mulai dengan pertanyaan yang lebih mudah untuk membangun kepercayaan diri
        - Secara bertahap tingkatkan kompleksitas pertanyaan
        - Berikan apresiasi untuk jawaban yang baik
        - Bantu kandidat jika mereka kesulitan dengan pertanyaan tertentu
        - Pastikan kandidat merasa nyaman dan termotivasi
        
        AKHIR SESI:
        - Berikan ringkasan performa secara keseluruhan
        - Highlight kekuatan dan area yang perlu diperbaiki
        - Berikan saran konkret untuk persiapan wawancara sesungguhnya
        - Tutup dengan motivasi dan dukungan
        """
        else:
            conversation_guidelines = """
        
        CONVERSATION GUIDELINES:
        - Use a natural, flowing conversational approach
        - Ask follow-up questions based on candidate responses
        - Provide constructive feedback and encouragement
        - Adjust difficulty level based on candidate responses
        - Ensure all major categories are covered within available duration
        - Create questions relevant to previous answer contexts
        - Don't rigidly follow predetermined question sequences
        
        INTERVIEW STYLE:
        - Start with easier questions to build confidence
        - Gradually increase question complexity
        - Acknowledge good answers with appreciation
        - Help candidates if they struggle with certain questions
        - Ensure candidates feel comfortable and motivated
        
        SESSION CONCLUSION:
        - Provide overall performance summary
        - Highlight strengths and areas for improvement
        - Give concrete advice for actual interview preparation
        - Close with motivation and support
        """
        
        system_prompt += conversation_guidelines
        
        return system_prompt

    @function_tool
    async def start_interview(self, context: RunContext):
        """Start the interview with a proper introduction."""

        if self.introduction_given:
            if self.language == 'id':
                return "Wawancara sudah dimulai. Mari kita lanjutkan."
            else:
                return "Interview has already started. Let's continue."

        # Mark introduction as given
        self.introduction_given = True

        if self.language == 'id':
            introduction = f"""
            Selamat datang {self.first_name}! Saya Terra, interviewer LPDP dengan pengalaman lebih dari 20 tahun.

            Hari ini kita akan melakukan sesi latihan wawancara LPDP selama {self.duration_minutes} menit.
            Saya akan mengajukan sekitar {self.estimated_questions} pertanyaan yang mencakup berbagai aspek penting
            untuk beasiswa LPDP.

            Sesi ini dirancang untuk membantu Anda mempersiapkan diri menghadapi wawancara sesungguhnya.
            Jangan ragu untuk menjawab dengan natural dan jujur. Saya akan memberikan feedback konstruktif
            untuk membantu Anda berkembang.

            Apakah Anda siap untuk memulai? Mari kita mulai dengan pertanyaan pertama.
            """
        else:
            introduction = f"""
            Welcome {self.first_name}! I'm Terra, an LPDP interviewer with over 20 years of experience.

            Today we'll conduct a {self.duration_minutes}-minute LPDP interview practice session.
            I'll ask approximately {self.estimated_questions} questions covering various important aspects
            of the LPDP scholarship.

            This session is designed to help you prepare for the actual interview.
            Feel free to answer naturally and honestly. I'll provide constructive feedback
            to help you improve.

            Are you ready to begin? Let's start with the first question.
            """

        logger.info(f"Introduction given to {self.first_name} in {self.language}")
        return introduction.strip()

    @function_tool
    async def get_next_question(self, context: RunContext, user_response: str = ""):
        """Generate the next question using conversation history with Gemini."""
        
        # Add user response to conversation history if provided
        if user_response.strip():
            self.conversation_history.append({
                "role": "user", 
                "content": user_response
            })
        
        # Create prompt for next question generation
        if self.language == 'id':
            prompt = f"""
            Berdasarkan percakapan sejauh ini, buatkan pertanyaan wawancara LPDP berikutnya yang:
            1. Natural dan mengalir dari konteks percakapan
            2. Sesuai dengan durasi interview ({self.duration_minutes} menit, target {self.estimated_questions} pertanyaan)
            3. Mencakup kategori yang belum banyak dibahas
            4. Disesuaikan dengan level respons kandidat sejauh ini
            
            Riwayat percakapan:
            {self._format_conversation_history()}
            
            Berikan HANYA pertanyaan berikutnya, tanpa penjelasan tambahan.
            """
        else:
            prompt = f"""
            Based on the conversation so far, create the next LPDP interview question that:
            1. Flows naturally from the conversation context
            2. Fits the interview duration ({self.duration_minutes} minutes, target {self.estimated_questions} questions)
            3. Covers categories that haven't been extensively discussed
            4. Matches the candidate's response level so far
            
            Conversation history:
            {self._format_conversation_history()}
            
            Provide ONLY the next question, without additional explanation.
            """
        
        # Generate next question using Gemini
        next_question = await generate_ai_response(prompt)
        
        # Add question to conversation history
        self.conversation_history.append({
            "role": "assistant",
            "content": next_question
        })
        
        logger.info(f"Generated next question: {next_question[:50]}...")
        return next_question

    def _format_conversation_history(self) -> str:
        """Format conversation history for prompt context."""
        if not self.conversation_history:
            if self.language == 'id':
                return "Belum ada percakapan sebelumnya. Ini adalah awal wawancara."
            else:
                return "No previous conversation. This is the start of the interview."
        
        formatted = ""
        for i, entry in enumerate(self.conversation_history[-6:], 1):  # Last 6 entries for context
            role = "Interviewer" if entry["role"] == "assistant" else "Candidate"
            formatted += f"{role}: {entry['content']}\n"
        
        return formatted

    @function_tool
    async def provide_feedback(self, context: RunContext, answer: str):
        """Provide constructive feedback on candidate's answer."""
        
        if self.language == 'id':
            feedback_prompt = f"""
            Berikan feedback singkat dan konstruktif untuk jawaban kandidat LPDP berikut:
            
            Jawaban: "{answer}"
            
            Fokus pada:
            1. Kekuatan dalam jawaban
            2. Area yang bisa diperbaiki
            3. Saran konkret untuk pengembangan
            
            Berikan feedback dalam 2-3 kalimat yang mendorong dan membangun.
            """
        else:
            feedback_prompt = f"""
            Provide brief and constructive feedback for this LPDP candidate's answer:
            
            Answer: "{answer}"
            
            Focus on:
            1. Strengths in the answer
            2. Areas for improvement
            3. Concrete suggestions for development
            
            Provide feedback in 2-3 encouraging and constructive sentences.
            """
        
        feedback = await generate_ai_response(feedback_prompt)
        logger.info(f"Generated feedback for answer: {answer[:30]}...")
        return feedback

    @function_tool
    async def conclude_interview(self, context: RunContext):
        """Provide final interview summary and recommendations."""
        
        conversation_summary = self._format_conversation_history()
        
        if self.language == 'id':
            conclusion_prompt = f"""
            Berdasarkan seluruh percakapan wawancara LPDP ini, buatkan kesimpulan yang mencakup:
            
            1. Ringkasan performa keseluruhan kandidat
            2. Kekuatan utama yang ditunjukkan
            3. Area yang perlu diperbaiki
            4. Saran konkret untuk persiapan wawancara sesungguhnya
            5. Motivasi dan dukungan untuk kandidat
            
            Percakapan lengkap:
            {conversation_summary}
            
            Berikan kesimpulan yang komprehensif namun ringkas (3-4 paragraf).
            """
        else:
            conclusion_prompt = f"""
            Based on this complete LPDP interview conversation, create a conclusion that includes:
            
            1. Overall candidate performance summary
            2. Main strengths demonstrated
            3. Areas needing improvement
            4. Concrete advice for actual interview preparation
            5. Motivation and support for the candidate
            
            Complete conversation:
            {conversation_summary}
            
            Provide a comprehensive yet concise conclusion (3-4 paragraphs).
            """
        
        conclusion = await generate_ai_response(conclusion_prompt)
        logger.info("Generated interview conclusion")
        return conclusion
