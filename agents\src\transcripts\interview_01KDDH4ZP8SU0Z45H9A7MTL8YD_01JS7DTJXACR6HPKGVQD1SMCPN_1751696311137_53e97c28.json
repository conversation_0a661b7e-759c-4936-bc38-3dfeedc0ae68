{"session_id": "interview_01KDDH4ZP8SU0Z45H9A7MTL8YD_01JS7DTJXACR6HPKGVQD1SMCPN_1751696311137_53e97c28", "last_updated": "2025-07-05T13:22:29.473823", "room_metadata": {"sessionId": "interview_01KDDH4ZP8SU0Z45H9A7MTL8YD_01JS7DTJXACR6HPKGVQD1SMCPN_1751696311137_53e97c28", "interviewId": "01KDDH4ZP8SU0Z45H9A7MTL8YD", "category": "LPDP", "type": "partial", "duration": "00:15:00", "interview_type": "partial", "category_name": "LPDP", "name": "LPDP Future Contributions", "subname": "Future Contributions to Indonesia Interview", "description": "Focused interview on your plans to contribute to Indonesia after completing your studies for LPDP scholarship", "sections": [{"id": "contributions", "name": "Future Contributions to Indonesia", "duration": "00:15:00"}], "created_at": "2025-07-05T06:18:31.144Z", "session_resumed": true, "resume_context": {"elapsed_minutes": 2.933333333333333, "remaining_minutes": 12.066666666666666, "estimated_questions_asked": 0, "is_near_end": false, "original_start_time": "2025-07-05T06:18:31.144Z", "resume_timestamp": "2025-07-05T06:21:27.900Z", "session_continuation": true}, "user_context": {"userEmail": "<EMAIL>", "userName": "Dark Souls AI", "userId": "01JS7DTJXACR6HPKGVQD1SMCPN", "sessionId": "interview_01KDDH4ZP8SU0Z45H9A7MTL8YD_01JS7DTJXACR6HPKGVQD1SMCPN_1751696311137_53e97c28", "participantIdentity": "user_daffa_terang_ai_1751696311137_53e97c28"}, "language": "en"}, "user_metadata": {"userEmail": "<EMAIL>", "userId": "01JS7DTJXACR6HPKGVQD1SMCPN", "userName": "Dark Souls AI", "interviewId": "01KDDH4ZP8SU0Z45H9A7MTL8YD", "category": "LPDP", "type": "partial", "sessionId": "interview_01KDDH4ZP8SU0Z45H9A7MTL8YD_01JS7DTJXACR6HPKGVQD1SMCPN_1751696311137_53e97c28", "isExistingSession": true, "sections": [{"id": "contributions", "name": "Future Contributions to Indonesia", "duration": "00:15:00"}], "sessionStartTime": "2025-07-05T06:18:31.144Z", "resumedAt": "2025-07-05T06:21:49.964Z", "elapsedMinutes": 3.3, "questionsAsked": 0, "remainingMinutes": 11.7, "session_context": {"questions_estimated": 0, "time_elapsed_minutes": 3.3, "time_remaining_minutes": 11.7, "is_continuation": true, "is_near_end": false}}, "transcript": [{"role": "interviewer", "content": "Halo Dark Souls AI, say<PERSON>, dan saya adalah pewawancara LPDP Anda untuk sesi hari ini di Terang AI. Selamat datang di latihan LPDP Future Contributions untuk kategori LPDP dengan fokus pada Future Contributions to Indonesia. Ini adalah sesi wawancara partial dengan durasi 15 menit yang akan membantu Anda mempersiapkan proses seleksi beasiswa LPDP yang sebenarnya. Saya akan menanyakan beberapa pertanyaan relevan selama waktu yang dialokasikan. <PERSON>hon jawab dengan jelas dan percaya diri seperti dalam wawancara yang sesungguhnya. Siap untuk memulai, Dark?", "timestamp": "2025-07-05T13:19:18.627525", "function": "conversation_item"}, {"role": "interviewer", "content": "Halo Dark Souls AI, say<PERSON>, dan saya adalah pewawancara LPDP Anda untuk sesi hari ini di Terang AI. Selamat datang di latihan LPDP Future Contributions untuk kategori LPDP dengan fokus pada Future Contributions to Indonesia. Ini adalah sesi wawancara partial dengan durasi 15 menit yang akan membantu Anda mempersiapkan proses seleksi beasiswa LPDP yang sebenarnya. Saya akan menanyakan beberapa pertanyaan relevan selama waktu yang dialokasikan. <PERSON>hon jawab dengan jelas dan percaya diri seperti dalam wawancara yang sesungguhnya. Siap untuk memulai, Dark?", "timestamp": "2025-07-05T13:19:20.715382", "function": "on_enter_new"}, {"role": "applicant", "content": "Ya tentu saya siap untuk memulai.", "timestamp": "2025-07-05T13:19:26.220668", "function": "conversation_item"}, {"role": "interviewer", "content": "<PERSON><PERSON>, <PERSON>! Mari mulai. Mari kita awali dengan perkenalan diri Anda, beritahu saya tentang diri Anda dan latar belakang <PERSON>.", "timestamp": "2025-07-05T13:19:29.112722", "function": "handle_readiness"}, {"role": "interviewer", "content": "<PERSON><PERSON>, <PERSON>! Mari mulai. Mari kita awali dengan perkenalan diri Anda, beritahu saya tentang diri Anda dan latar belakang <PERSON>.\n", "timestamp": "2025-07-05T13:19:41.450396", "function": "conversation_item"}, {"role": "applicant", "content": "<PERSON>a saya adalah.", "timestamp": "2025-07-05T13:19:49.803614", "function": "conversation_item"}, {"role": "applicant", "content": "Dark seperti yang tadi sudah anda sebutkan.", "timestamp": "2025-07-05T13:19:59.497997", "function": "conversation_item"}, {"role": "applicant", "content": "Dan jadi kalau dari diri saya ya, saya adalah seorang mahasiswa teknik komputer Universitas Indonesia.", "timestamp": "2025-07-05T13:20:03.078601", "function": "conversation_item"}, {"role": "applicant", "content": "Dengan latar belakang adalah. Seorang.", "timestamp": "2025-07-05T13:20:08.168137", "function": "conversation_item"}, {"role": "applicant", "content": "Asisten lab digital laboratorium.", "timestamp": "2025-07-05T13:20:10.489194", "function": "conversation_item"}, {"role": "interviewer", "content": "<PERSON><PERSON>, <PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> ma<PERSON>ah teknis. Mari kita coba lagi.\n\nSebagai penerima beasiswa LPDP, bagaimana Anda berencana untuk berkontribusi pada Indonesia setelah menyelesaikan studi Anda?", "timestamp": "2025-07-05T13:20:29.043141", "function": "conversation_item"}, {"role": "interviewer", "content": "Welcome back, <PERSON>! I'm Professor <PERSON>, and we're continuing your LPDP interview session. We've covered 3 questions in our previous 3 minutes together. Let me continue with our next question.", "timestamp": "2025-07-05T13:22:08.811764", "function": "conversation_item"}, {"role": "interviewer", "content": "Welcome back, <PERSON>! I'm Professor <PERSON>, and we're continuing your LPDP interview session. We've covered 3 questions in our previous 3 minutes together. Let me continue with our next question.", "timestamp": "2025-07-05T13:22:10.652353", "function": "on_enter_resumed"}, {"role": "interviewer", "content": "Okay, that's helpful. So, you mentioned \"Asisten lab digital laboratorium\" (Digital Lab Assistant).\n\nTo delve a little deeper, **could you tell me about a specific challenge you faced as a Digital Lab Assistant and how you overcame it?**", "timestamp": "2025-07-05T13:22:11.568089", "function": "continue_resume_session"}, {"role": "interviewer", "content": "Okay, that's helpful. So, you mentioned \"Asisten lab digital laboratorium\" (Digital Lab Assistant).\n\nTo delve a little deeper, **could you tell me about a specific challenge you faced as a Digital Lab Assistant and how you overcame it?**", "timestamp": "2025-07-05T13:22:29.473410", "function": "conversation_item"}], "session_summary": {"user_name": "Dark Souls AI", "user_email": "<EMAIL>", "interview_id": "01KDDH4ZP8SU0Z45H9A7MTL8YD", "elapsed_minutes": 3.1807715666666665, "total_entries": 15, "recording_info": {"egress_id": "EG_p8BVYwjAugTU", "path": "recordings/interview_01KDDH4ZP8SU0Z45H9A7MTL8YD_01JS7DTJXACR6HPKGVQD1SMCPN_1751696311137_53e97c28/recording.ogg"}}}