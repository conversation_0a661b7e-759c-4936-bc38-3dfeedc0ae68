import json
import os
import traceback
from google.cloud import storage
from google.oauth2 import service_account
from .config import logger, GOOGLE_JSON_CREDS, GCS_BUCKET_NAME

def get_gcs_client():
    """Initializes and returns a GCS client."""
    try:
        if GOOGLE_JSON_CREDS:
            try:
                credentials_info = json.loads(GOOGLE_JSON_CREDS)
                credentials = service_account.Credentials.from_service_account_info(credentials_info)
                return storage.Client(credentials=credentials)
            except json.JSONDecodeError:
                return storage.Client.from_service_account_json(GOOGLE_JSON_CREDS)
        else:
            return storage.Client()
    except Exception as e:
        logger.error(f"Failed to create GCS client: {e}")
        return None

async def upload_transcript_to_gcs(session_id: str, transcript_file_path: str):
    """Upload transcript file to GCS bucket."""
    if not os.path.exists(transcript_file_path):
        logger.error(f"Transcript file not found for upload: {transcript_file_path}")
        return False

    storage_client = get_gcs_client()
    if not storage_client:
        logger.error("Cannot upload transcript, GCS client initialization failed.")
        return False

    try:
        bucket = storage_client.bucket(GCS_BUCKET_NAME)
        
        # Upload JSON transcript
        gcs_json_path = f"recordings/{session_id}/transcript.json"
        blob_json = bucket.blob(gcs_json_path)
        blob_json.upload_from_filename(transcript_file_path)
        logger.info(f"Uploaded JSON transcript to gs://{GCS_BUCKET_NAME}/{gcs_json_path}")

        # Upload text transcript if it exists
        text_file_path = transcript_file_path.replace('.json', '.txt')
        if os.path.exists(text_file_path):
            gcs_text_path = f"recordings/{session_id}/transcript.txt"
            blob_text = bucket.blob(gcs_text_path)
            blob_text.upload_from_filename(text_file_path)
            logger.info(f"Uploaded text transcript to gs://{GCS_BUCKET_NAME}/{gcs_text_path}")
            
        return True
    except Exception as e:
        logger.error(f"Error uploading transcript to GCS: {e}")
        logger.error(traceback.format_exc())
        return False