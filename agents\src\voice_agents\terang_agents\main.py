import asyncio
import json
import time
from datetime import datetime

from livekit.agents import (
    AgentSession,
    JobContext,
    JobProcess,
    RoomInputOptions,
    RoomOutputOptions,
    WorkerOptions,
    cli,
)
from livekit.plugins import azure, openai, silero, noise_cancellation, google

from .agents.lpdp_agent import LPDPInterviewAgent
from .agents.simple_lpdp_agent import SimpleLPDPAgent
from .configs.config import (
    logger,
    AZURE_SPEECH_KEY,
    AZURE_SPEECH_REGION,
    AZURE_OPENAI_TTS_DEPLOYMENT,
    CUSTOM_AZURE_OPENAI_ENDPOINT,
    CUSTOM_AZURE_OPENAI_API_KEY,
    AZURE_OPENAI_API_VERSION,
)
from .configs.livekit_utils import setup_livekit_recording, stop_livekit_recording
from .transcript_manager import TranscriptManager, clear_global_transcript

def prewarm(proc: JobProcess):
    """Prewarms necessary components before the agent starts."""
    proc.userdata["vad"] = silero.VAD.load()
    logger.info("VAD model prewarmed.")

def get_session_id(room_config: dict, user_metadata: dict) -> str:
    """Extracts or creates a unique session ID."""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    # Priority: Use sessionId from room config
    if room_config.get('sessionId'):
        return room_config['sessionId']
    
    # Fallback: Create a new session ID
    user_email = user_metadata.get('userEmail', f"anon_{timestamp}")
    email_safe = user_email.replace('@', '_').replace('.', '_')
    return f"session_{email_safe}_{int(time.time())}"

def get_tts_instructions(language='en'):
    """Returns language-specific instructions for the TTS model."""
    if language == 'id':
        return "You are speaking Indonesian. Pronounce words naturally. Maintain a professional, warm tone."
    return "You are conducting a professional interview. Speak clearly and maintain a warm, encouraging tone."

async def entrypoint(ctx: JobContext):
    """The main entrypoint for the voice agent job."""
    await ctx.connect()
    logger.info("Successfully connected to the room.")

    room_metadata = json.loads(ctx.room.metadata) if ctx.room.metadata else {}
    
    # Wait for a participant to join to get user metadata
    user_metadata = {}
    try:
        participant = await asyncio.wait_for(ctx.wait_for_participant(), timeout=30.0)
        if participant and participant.metadata:
            user_metadata = json.loads(participant.metadata)
            logger.info(f"Extracted user metadata: {user_metadata}")
    except asyncio.TimeoutError:
        logger.error("Timeout waiting for a participant to join.")
        return
    except Exception as e:
        logger.error(f"Error getting participant metadata: {e}")

    # For new sessions, clear any lingering transcript data
    if not room_metadata.get('session_resumed', False):
        clear_global_transcript()

    session_id = get_session_id(room_metadata, user_metadata)
    logger.info(f"Initialized session with ID: {session_id}")

    # Initialize the transcript manager
    transcript_manager = TranscriptManager(session_id, room_metadata, user_metadata)
    if room_metadata.get('session_resumed', False):
        transcript_manager.load_existing_transcript()

    # Setup LiveKit recording
    egress_id, recording_path = await setup_livekit_recording(ctx, session_id)
    if egress_id:
        transcript_manager.set_recording_details(egress_id, recording_path)
        ctx.add_shutdown_callback(lambda: stop_livekit_recording(egress_id))
    
    # Always ensure final transcript is saved and uploaded on shutdown
    ctx.add_shutdown_callback(transcript_manager.final_transcript_upload)

    # Initialize the agent
    lpdp_agent = LPDPInterviewAgent(
        room_metadata=ctx.room.metadata,
        user_metadata=user_metadata,
        transcript_manager=transcript_manager
    )

    language = lpdp_agent.language
    stt_languages = ["id-ID"] if language == "id" else ["en-US", "id-ID"]

    session = AgentSession(
        vad=ctx.proc.userdata["vad"],
        llm=google.LLM(model='gemini-2.0-flash-001'),
        stt=azure.STT(
            speech_key=AZURE_SPEECH_KEY,
            speech_region=AZURE_SPEECH_REGION,
            language=stt_languages,
        ),
        tts=openai.TTS.with_azure(
            model=AZURE_OPENAI_TTS_DEPLOYMENT,
            azure_endpoint=CUSTOM_AZURE_OPENAI_ENDPOINT,
            api_key=CUSTOM_AZURE_OPENAI_API_KEY,
            api_version=AZURE_OPENAI_API_VERSION,
            voice="nova",
            speed=2.0,
            instructions=get_tts_instructions(language),
        ),
        turn_detection="vad",
        allow_interruptions=True,
        min_interruption_duration=0.8,
    )

    # Add event handler to capture both user and agent conversation items
    @session.on("conversation_item_added")
    def on_conversation_item_added(event):
        """Handle both user and agent conversation items"""
        try:
            # Determine role based on the event item
            role = "applicant" if event.item.role == "user" else "interviewer"

            # Get text content from the item
            text_content = event.item.text_content if hasattr(event.item, 'text_content') else str(event.item)

            # Skip if content is empty or just whitespace
            if not text_content or not text_content.strip():
                logger.info(f"Skipping empty {role} content")
                return

            # Add to transcript using transcript manager
            transcript_manager.add_entry(role, text_content, "conversation_item")

            user_name = user_metadata.get('userName', 'Unknown User')
            user_email = user_metadata.get('userEmail', '<EMAIL>')
            session_type = "RESUMED" if room_metadata.get('session_resumed') else "NEW"
            logger.info(f"{role} speech from {user_name} ({user_email}) [{session_type}]: {text_content}")

        except Exception as e:
            logger.error(f"Error in conversation_item_added handler: {e}")

    logger.info("Starting agent session...")
    await session.start(
        agent=lpdp_agent,
        room=ctx.room,
        room_input_options=RoomInputOptions(noise_cancellation=noise_cancellation.BVC()),
        room_output_options=RoomOutputOptions(transcription_enabled=True),
    )
    logger.info("Agent session finished.")

if __name__ == "__main__":
    cli.run_app(WorkerOptions(
        entrypoint_fnc=entrypoint, 
        prewarm_fnc=prewarm,
        shutdown_process_timeout=120
    ))