# Multi-Language Question Bank Implementation

## Overview

Fitur multi-bahasa telah diimplementasikan pada `question_bank.py` untuk mendukung pertanyaan interview dalam Bahasa Indonesia dan English. Sistem akan secara otomatis memilih set pertanyaan yang sesuai berdasarkan bahasa yang dipilih dalam sesi interview.

## Implementasi

### 1. Struktur Question Bank

#### `BASE_QUESTIONS_EN`
- <PERSON><PERSON>i semua pertanyaan dalam Bahasa Inggris
- Diorganisir berdasarkan kategori: personal, study_plans, contributions, qualifications, leadership, knowledge_indonesia

#### `BASE_QUESTIONS_ID`
- <PERSON><PERSON>i terjemahan lengkap semua pertanyaan dalam Bahasa Indonesia
- Struktur kategori yang sama dengan versi English
- Terjemahan yang natural dan sesuai konteks LPDP Indonesia

### 2. Function Updates

#### `get_questions_for_interview_type()`
```python
def get_questions_for_interview_type(interview_type: str, category_name: str, sections: list = None, language: str = 'en') -> dict:
```

**Parameter baru:**
- `language`: Parameter untuk menentukan bahasa ('en' atau 'id')
- Default: 'en' untuk backward compatibility

**Logic:**
```python
BASE_QUESTIONS = BASE_QUESTIONS_ID if language == 'id' else BASE_QUESTIONS_EN
```

### 3. Integration dengan Agent

#### `lpdp_agent.py`
- Pemanggilan `get_questions_for_interview_type()` diperbarui untuk menyertakan parameter `self.language`
- Language detection dari room configuration: `self.language = self.room_config.get('language', 'en')`

## Kategori Pertanyaan

### 1. Personal Background (`personal`)
**English Examples:**
- "Tell me about yourself and your background."
- "What are your biggest strengths and weaknesses?"
- "What motivates you to pursue higher education?"

**Indonesian Examples:**
- "Ceritakan tentang diri Anda dan latar belakang Anda."
- "Apa kekuatan dan kelemahan terbesar Anda?"
- "Apa yang memotivasi Anda untuk mengejar pendidikan tinggi?"

### 2. Study Plans (`study_plans`)
**English Examples:**
- "Why did you choose this specific program and university?"
- "How does this program align with your previous education and experience?"

**Indonesian Examples:**
- "Mengapa Anda memilih program dan universitas yang spesifik ini?"
- "Bagaimana program ini sejalan dengan pendidikan dan pengalaman sebelumnya?"

### 3. Future Contributions (`contributions`)
**English Examples:**
- "How will you contribute to Indonesia after completing your studies?"
- "What specific problems in Indonesia do you hope to address with your education?"

**Indonesian Examples:**
- "Bagaimana Anda akan berkontribusi kepada Indonesia setelah menyelesaikan studi?"
- "Masalah spesifik apa di Indonesia yang ingin Anda atasi dengan pendidikan Anda?"

### 4. Academic Qualifications (`qualifications`)
**English Examples:**
- "Tell me about your academic achievements."
- "How did you maintain your GPA while participating in other activities?"

**Indonesian Examples:**
- "Ceritakan tentang pencapaian akademik Anda."
- "Bagaimana Anda mempertahankan IPK sambil berpartisipasi dalam kegiatan lain?"

### 5. Leadership Experience (`leadership`)
**English Examples:**
- "Tell me about your leadership experiences."
- "How do you define good leadership?"

**Indonesian Examples:**
- "Ceritakan tentang pengalaman kepemimpinan Anda."
- "Bagaimana Anda mendefinisikan kepemimpinan yang baik?"

### 6. Indonesia Knowledge (`knowledge_indonesia`)
**English Examples:**
- "What do you think are Indonesia's most pressing challenges right now?"
- "What is your understanding of Pancasila and how do you apply its principles?"

**Indonesian Examples:**
- "Menurut Anda, apa tantangan paling mendesak Indonesia saat ini?"
- "Apa pemahaman Anda tentang Pancasila dan bagaimana Anda menerapkan prinsip-prinsipnya?"

## Usage Examples

### 1. English Interview
```python
questions = get_questions_for_interview_type("full", "LPDP", [], "en")
# Returns: BASE_QUESTIONS_EN with all categories
```

### 2. Indonesian Interview
```python
questions = get_questions_for_interview_type("full", "LPDP", [], "id")
# Returns: BASE_QUESTIONS_ID with all categories
```

### 3. Partial Interview with Language
```python
sections = [{"id": "personal"}, {"id": "study_plans"}]
questions = get_questions_for_interview_type("partial", "Personal Background", sections, "id")
# Returns: Indonesian questions for personal and study_plans categories only
```

## Benefits

### 1. Natural Language Experience
- Kandidat dapat menggunakan bahasa yang lebih nyaman
- Pertanyaan dalam Bahasa Indonesia lebih natural untuk konteks LPDP
- Mengurangi barrier bahasa dalam interview practice

### 2. Consistent Quality
- Terjemahan yang konsisten dan professional
- Terminologi yang sesuai dengan konteks beasiswa LPDP Indonesia
- Struktur pertanyaan yang tetap komprehensif

### 3. Backward Compatibility
- Default language tetap English untuk compatibility
- Existing code tidak perlu diubah jika tidak menggunakan fitur multi-bahasa
- Smooth migration path

### 4. Scalability
- Mudah untuk menambahkan bahasa lain di masa depan
- Struktur yang modular dan maintainable
- Consistent API interface

## Technical Notes

- Total 15 pertanyaan per kategori untuk setiap bahasa
- 6 kategori utama dengan total 90 pertanyaan per bahasa
- Memory efficient dengan lazy loading berdasarkan bahasa yang dipilih
- Thread-safe implementation
- Logging yang informatif untuk debugging dan monitoring

## Future Enhancements

1. **Dynamic Translation**: Integrasi dengan translation API untuk bahasa tambahan
2. **Regional Dialects**: Support untuk dialek regional Indonesia
3. **Question Difficulty Levels**: Pertanyaan dengan tingkat kesulitan berbeda
4. **Custom Question Sets**: Kemampuan untuk custom question sets per institusi
