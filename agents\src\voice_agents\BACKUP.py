import logging
import os
import json
import traceback
from datetime import datetime, timedelta
import sys
import time

from dotenv import load_dotenv

from livekit.agents import (
    Agent,
    AgentSession,
    JobContext,
    JobProcess,
    RoomInputOptions,
    RoomOutputOptions,
    RunContext,
    WorkerOptions,
    cli,
)

from livekit.agents.llm import function_tool
from livekit.plugins import openai, silero
from livekit.plugins import noise_cancellation
from livekit.plugins import google
from livekit.plugins import azure
import random
import asyncio
from livekit import api  # Import LiveKit API for Egress

# Add Google Generative AI client .
import google.generativeai as genai

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger("lpdp-interview-agent")

load_dotenv()

# Configure Google AI
google_api_key = os.getenv("GOOGLE_API_KEY")
if google_api_key:
    genai.configure(api_key=google_api_key)
    logger.info("Google AI configured successfully")
else:
    logger.error("GOOGLE_API_KEY not found in environment variables")

# Create absolute path for transcripts directory
TRANSCRIPTS_DIR = os.path.join(os.getcwd(), "transcripts")
try:
    os.makedirs(TRANSCRIPTS_DIR, exist_ok=True)
    logger.info(f"Transcript directory created at: {TRANSCRIPTS_DIR}")
except Exception as e:
    logger.error(f"Failed to create transcript directory: {e}")
    logger.error(traceback.format_exc())

# Global transcript storage - shared across all components
GLOBAL_TRANSCRIPT = []
TRANSCRIPT_FILENAME = ""
TEXT_TRANSCRIPT_FILENAME = ""

# Initialize a global model instance for reuse (faster than creating new instance each time)
global_model = None
if google_api_key:
    try:
        global_model = genai.GenerativeModel(
            'gemini-2.0-flash-001',  # Using faster flash model instead of 2.0-flash-exp
            generation_config=genai.types.GenerationConfig(
                temperature=0.7,  # Slightly reduced for faster generation
                max_output_tokens=4000,  # Reduced for faster generation
                top_p=0.95,
                top_k=40,
                candidate_count=1,
            )
        )
        logger.info("Global Gemini model initialized successfully")
    except Exception as e:
        logger.error(f"Error initializing global model: {e}")

async def setup_livekit_recording(ctx, session_id, bucket_name="terang-ai-assets", region="asia-southeast2"):
    """Set up LiveKit Egress recording for the session with improved path structure"""
    try:
        # Check for LiveKit URL
        livekit_url = os.getenv("LIVEKIT_URL")
        if not livekit_url:
            logger.error("LIVEKIT_URL environment variable not set")
            return None
            
        logger.info(f"Setting up LiveKit Egress recording for session {session_id}")
        logger.info(f"Room name: {ctx.room.name}")
        logger.info(f"LiveKit URL: {livekit_url}")

        gcp_creds = os.getenv("GOOGLE_JSON_CREDS")

        # Use session_id as directory in path structure
        recording_path = f"recordings/{session_id}/recording.ogg"
        logger.info(f"Recording will be saved to path: {recording_path}")

        # Set up recording configuration
        req = api.RoomCompositeEgressRequest(
            room_name=ctx.room.name,
            audio_only=True,  # Set to False if you want video recording too
            file_outputs=[api.EncodedFileOutput(
                file_type=api.EncodedFileType.OGG,
                filepath=recording_path,  # Changed path structure
                gcp=api.GCPUpload(
                    bucket=bucket_name,
                    credentials=gcp_creds,
                ),
            )],
        )

        # Initialize LiveKit API with URL
        logger.info("Initializing LiveKit API client...")
        lkapi = api.LiveKitAPI(livekit_url)
        
        logger.info("Starting room composite egress...")
        res = await lkapi.egress.start_room_composite_egress(req)
        
        logger.info(f"Started LiveKit Egress recording with ID: {res.egress_id}")
        logger.info(f"Recording will be saved to GCS bucket: {bucket_name}/{recording_path}")
        
        # Close the API connection
        await lkapi.aclose()
        logger.info("LiveKit API connection closed")
        
        return res.egress_id, recording_path
    except Exception as e:
        logger.error(f"Error setting up LiveKit Egress recording: {e}")
        logger.error(f"Error type: {type(e).__name__}")
        logger.error(traceback.format_exc())
        return None, None

# New function to upload transcript to GCS
async def upload_transcript_to_gcs(session_id, transcript_file_path, bucket_name="terang-ai-assets"):
    """Upload transcript file to GCS bucket using the same path structure as recording"""
    try:
        from google.cloud import storage
        import json
        import os
        
        # Check if transcript file exists
        if not os.path.exists(transcript_file_path):
            logger.error(f"Transcript file not found: {transcript_file_path}")
            return False
            
        # Determine GCS path - use session_id as directory
        gcs_transcript_path = f"recordings/{session_id}/transcript.json"
        gcs_text_path = f"recordings/{session_id}/transcript.txt"
        
        logger.info(f"Uploading transcript to GCS: {bucket_name}/{gcs_transcript_path}")
        
        # Get the GCP credentials from environment
        gcp_creds = os.getenv("GOOGLE_JSON_CREDS")
        
        # Initialize GCS client with the same credentials used for LiveKit
        if gcp_creds:
            # If credentials are provided as JSON string, create a temp file or use it directly
            import tempfile
            import io
            from google.oauth2 import service_account
            
            # Create credentials object from the JSON
            try:
                credentials_info = json.loads(gcp_creds)
                credentials = service_account.Credentials.from_service_account_info(credentials_info)
                storage_client = storage.Client(credentials=credentials)
                logger.info("Created storage client with provided JSON credentials")
            except json.JSONDecodeError:
                # If it's a file path instead of JSON string
                storage_client = storage.Client.from_service_account_json(gcp_creds)
                logger.info("Created storage client from credentials file")
        else:
            # Fall back to default credentials if no explicit credentials provided
            storage_client = storage.Client()
            logger.info("Created storage client with default credentials")
        
        bucket = storage_client.bucket(bucket_name)
        
        # Upload JSON transcript
        if transcript_file_path.endswith('.json'):
            blob = bucket.blob(gcs_transcript_path)
            blob.upload_from_filename(transcript_file_path)
            logger.info(f"Uploaded JSON transcript to {bucket_name}/{gcs_transcript_path}")
            
            # Also upload the text version if it exists
            text_file_path = transcript_file_path.replace('.json', '.txt')
            if os.path.exists(text_file_path):
                text_blob = bucket.blob(gcs_text_path)
                text_blob.upload_from_filename(text_file_path)
                logger.info(f"Uploaded text transcript to {bucket_name}/{gcs_text_path}")
        
        return True
    except Exception as e:
        logger.error(f"Error uploading transcript to GCS: {e}")
        logger.error(traceback.format_exc())
        return False

async def stop_livekit_recording(egress_id):
    """Stop the LiveKit Egress recording"""
    if not egress_id:
        logger.warning("No egress_id provided, cannot stop recording")
        return False
        
    try:
        # Check for LiveKit URL
        livekit_url = os.getenv("LIVEKIT_URL")
        if not livekit_url:
            logger.error("LIVEKIT_URL environment variable not set")
            return False
            
        logger.info(f"Stopping LiveKit Egress recording: {egress_id}")
        
        # Initialize LiveKit API with URL
        lkapi = api.LiveKitAPI(livekit_url)
        
        # Create the proper request object instead of passing string directly
        stop_request = api.StopEgressRequest(egress_id=egress_id)
        await lkapi.egress.stop_egress(stop_request)
        
        logger.info(f"Stopped LiveKit Egress recording: {egress_id}")
        
        await lkapi.aclose()
        return True
    except Exception as e:
        # Handle the case where recording already completed automatically
        if "EGRESS_COMPLETE" in str(e):
            logger.info(f"LiveKit Egress recording {egress_id} already completed automatically - this is normal for short sessions")
            return True
        else:
            logger.error(f"Error stopping LiveKit Egress recording: {e}")
            logger.error(f"Error type: {type(e).__name__}")
            logger.error(traceback.format_exc())
            return False

def parse_duration_to_minutes(duration_str):
    """Parse duration string (HH:MM:SS) to minutes"""
    try:
        parts = duration_str.split(':')
        if len(parts) == 3:
            hours, minutes, seconds = map(int, parts)
            return hours * 60 + minutes + seconds / 60
        elif len(parts) == 2:
            minutes, seconds = map(int, parts)
            return minutes + seconds / 60
        else:
            return 60  # Default fallback
    except:
        return 60  # Default fallback

def get_questions_for_interview_type(interview_type, category_name, sections=None):
    """Return filtered question sets based on interview type, category, and sections"""
    
    base_questions = {
        "personal": [
            "Tell me about yourself and your background.",
            "What are your biggest strengths and weaknesses?",
            "What motivates you to pursue higher education?",
            "What is your biggest achievement so far?",
            "How would your friends describe you?",
            "What challenges have you overcome in your life?",
            "Who is your role model and why?",
            "What makes you special compared to other applicants?",
            "How do you handle failure or setbacks?",
            "What are your long-term career goals?",
            "How have your past experiences prepared you for this scholarship opportunity?",
            "What values do you consider most important in your life?",
            "What drives your passion for your chosen field of study?",
            "Tell me about a time when you had to step out of your comfort zone.",
            "What personal qualities make you a good candidate for LPDP?"
        ],
        "study_plans": [
            "Why did you choose this specific program and university?",
            "Why do you want to study in this country rather than in Indonesia?",
            "How does this program align with your previous education and experience?",
            "How will you ensure you can complete your studies on time?",
            "What specific courses or research areas interest you the most?",
            "Have you contacted any professors at your target university?",
            "What is your research or thesis plan?",
            "How will you adapt to the educational system abroad?",
            "Why not pursue your master's degree at your previous university?",
            "What will you do if you face academic difficulties during your studies?",
            "How did you research your target university and program?",
            "What makes this university's approach to your field unique?",
            "How have you prepared academically for this program?",
            "What do you know about the specific faculty members in your department?",
            "How do you plan to manage your time during your studies?"
        ],
        "contributions": [
            "How will you contribute to Indonesia after completing your studies?",
            "What specific problems in Indonesia do you hope to address with your education?",
            "How will your chosen field benefit Indonesia's development?",
            "What is your 5-year and 10-year plan after graduation?",
            "How will you ensure the knowledge you gain abroad is applicable to Indonesia?",
            "What concrete plans do you have to implement your knowledge in Indonesia?",
            "How will you maintain your commitment to return to Indonesia?",
            "How do you plan to share your knowledge with others in Indonesia?",
            "What impact do you hope to make on your field in Indonesia?",
            "How does your study plan align with Indonesia's national priorities?",
            "What specific institutions or organizations in Indonesia could benefit from your expertise?",
            "How will you adapt international best practices to the Indonesian context?",
            "What networks do you plan to build while studying that could benefit Indonesia?",
            "How will you help bridge international collaboration in your field?",
            "What innovation could you bring to Indonesia after your studies?"
        ],
        "qualifications": [
            "Tell me about your academic achievements.",
            "How did you maintain your GPA while participating in other activities?",
            "Tell me about your research experience or previous thesis.",
            "What makes you academically prepared for this program?",
            "How have you continued developing your knowledge since graduation?",
            "What relevant skills do you have for your chosen field of study?",
            "Have you published any papers or participated in academic conferences?",
            "How will your previous education help you in your future studies?",
            "What academic challenges do you anticipate, and how will you address them?",
            "Why should LPDP invest in your education?",
            "What specific technical or specialized skills do you possess?",
            "How have you demonstrated your ability to succeed in an academic environment?",
            "What relevant professional certifications or training have you completed?",
            "How have you applied theoretical knowledge in practical situations?",
            "What makes you stand out academically from other candidates?"
        ],
        "leadership": [
            "Tell me about your leadership experiences.",
            "What organizations have you been involved with?",
            "How do you define good leadership?",
            "Tell me about a time when you led a team through a difficult situation.",
            "What social activities or community service have you participated in?",
            "How have you contributed to solving problems in your community?",
            "What initiatives have you started or been part of?",
            "How do you handle conflicts within a team?",
            "What is your approach to managing and motivating others?",
            "How have you demonstrated your commitment to serving others?",
            "Describe a situation where you had to make a difficult leadership decision.",
            "How do you balance being authoritative and collaborative as a leader?",
            "Tell me about a time you had to lead people with different backgrounds or perspectives.",
            "What leadership skills do you hope to develop further?",
            "How do you determine the success of your leadership in a project or organization?"
        ],
        "knowledge_indonesia": [
            "What do you think are Indonesia's most pressing challenges right now?",
            "How do you express your love for Indonesia?",
            "What does nationalism mean to you?",
            "How do you view Indonesia's position in the global community?",
            "What areas of infrastructure do we need to develop in Indonesia?",
            "What is your understanding of Pancasila and how do you apply its principles?",
            "What do you know about current economic conditions in Indonesia?",
            "How can your field contribute to Indonesia's sustainable development?",
            "What is your perspective on diversity in Indonesia?",
            "How would you contribute to improving education in Indonesia?",
            "What do you think about Indonesia's digital transformation efforts?",
            "How should Indonesia balance economic development with environmental protection?",
            "What role should Indonesia play in ASEAN and international relations?",
            "How can Indonesia improve its research and innovation capacity?",
            "What strategies could help reduce economic inequality in Indonesia?"
        ]
    }
    
    # For partial interviews, focus on specific categories
    if interview_type == "partial":
        # First check if sections are provided and use them
        if sections and isinstance(sections, list) and len(sections) > 0:
            # Extract section IDs from the sections data
            section_ids = [section.get('id') for section in sections if 'id' in section]
            logger.info(f"Using section IDs from room config: {section_ids}")
            
            # Filter questions based on section IDs
            if section_ids:
                filtered_questions = {}
                for section_id in section_ids:
                    if section_id in base_questions:
                        filtered_questions[section_id] = base_questions[section_id]
                
                if filtered_questions:
                    logger.info(f"Filtered questions by sections: {list(filtered_questions.keys())}")
                    return filtered_questions
        
        # Fallback to category name mapping if sections aren't usable
        category_mapping = {
            "Personal Background": ["personal"],
            "Study Plans": ["study_plans"], 
            "Future Contributions": ["contributions"],
            "Academic Qualifications": ["qualifications"],
            "Leadership Experience": ["leadership"],
            "Indonesia Knowledge": ["knowledge_indonesia"]
        }
        
        # Get relevant categories for this partial interview
        relevant_categories = category_mapping.get(category_name, ["personal"])
        logger.info(f"Using category mapping for {category_name}: {relevant_categories}")
        
        # Return only questions from relevant categories
        filtered_questions = {}
        for category in relevant_categories:
            if category in base_questions:
                filtered_questions[category] = base_questions[category]
        
        logger.info(f"Filtered questions by category name: {list(filtered_questions.keys())}")
        return filtered_questions or {"personal": base_questions["personal"]}
    
    # For full interviews, return all questions
    logger.info("Full interview type: providing all question categories")
    return base_questions

async def generate_ai_response(prompt: str, max_retries: int = 2) -> str:
    """Generate AI response using Google Generative AI directly with optimizations for speed"""
    global global_model
    
    if not global_model or not google_api_key:
        return "AI generation not available"
    
    for attempt in range(max_retries + 1):
        try:
            # Use asyncio timeout to prevent hanging
            response = await asyncio.wait_for(
                asyncio.to_thread(global_model.generate_content, prompt),
                timeout=8.0  # 8 second timeout for faster response
            )
            
            if response and response.text:
                return response.text.strip()
            else:
                return "No response generated"
                
        except asyncio.TimeoutError:
            logger.warning(f"AI generation timeout on attempt {attempt + 1}")
            if attempt == max_retries:
                return "AI response timeout - using fallback"
        except Exception as e:
            logger.error(f"Error generating AI response (attempt {attempt + 1}): {e}")
            if attempt == max_retries:
                return "AI generation temporarily unavailable"
    
    return "AI generation failed"

def load_existing_transcript(session_id):
    """Load existing transcript from file if it exists"""
    try:
        transcript_file = os.path.join(TRANSCRIPTS_DIR, f"{session_id}.json")
        if os.path.exists(transcript_file):
            with open(transcript_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
                transcript = data.get('transcript', [])
                
                # Extract session summary with elapsed minutes
                session_summary = data.get('session_summary', {})
                elapsed_minutes = session_summary.get('elapsed_minutes', 0)
                
                logger.info(f"Loaded existing transcript with {len(transcript)} entries")
                logger.info(f"Loaded elapsed_minutes from transcript: {elapsed_minutes:.1f}")
                
                # Add elapsed minutes to the data dictionary
                if 'elapsed_minutes' not in data:
                    data['elapsed_minutes'] = elapsed_minutes
                
                return transcript, data
        else:
            logger.info(f"No existing transcript found for session {session_id}")
            return [], {}
    except Exception as e:
        logger.error(f"Error loading existing transcript: {e}")
        return [], {}

def analyze_session_state_from_transcript(transcript, room_config):
    """Analyze the transcript to determine the current session state"""
    if not transcript:
        return {
            "is_resuming": False,  # Explicitly False for empty transcript
            "introduction_completed": False,
            "introduction_asked": False,
            "questions_asked": 0,
            "last_category": None,
            "last_question": None,
            "last_answer": None
        }
    
    # FIXED: Only consider it resuming if explicitly flagged AND has meaningful content
    session_resumed_flag = room_config.get('session_resumed', False)
    has_meaningful_content = len(transcript) > 2  # More than just welcome messages
    
    introduction_completed = False
    introduction_asked = False
    questions_asked = 0
    last_category = None
    last_question = None
    last_answer = None
    
    # Track conversation flow
    interviewer_entries = [entry for entry in transcript if entry.get('role') == 'interviewer']
    applicant_entries = [entry for entry in transcript if entry.get('role') == 'applicant']
    
    # Check if welcome message was already given
    if interviewer_entries:
        first_message = interviewer_entries[0].get('content', '').lower()
        if any(word in first_message for word in ['hello', 'welcome', 'terra', 'ready to begin']):
            introduction_completed = True
    
    # Check if introduction question was asked
    for entry in interviewer_entries:
        content = entry.get('content', '').lower()
        if 'tell me about yourself' in content or 'your background' in content:
            introduction_asked = True
            break
    
    # Count actual interview questions (excluding welcome and introduction)
    interview_question_indicators = [
        'why did you choose', 'how will you contribute', 'what are your', 
        'tell me about your', 'how do you', 'what makes you',
        'describe your', 'what is your plan', 'how have you'
    ]
    
    for entry in interviewer_entries:
        content = entry.get('content', '').lower()
        # Skip welcome messages and introduction prompts
        if any(word in content for word in ['hello', 'welcome', 'ready to begin']):
            continue
        if 'tell me about yourself' in content:
            continue
            
        # Check if it's an actual interview question
        if any(indicator in content for indicator in interview_question_indicators) or content.endswith('?'):
            questions_asked += 1
            last_question = entry.get('content')
    
    # Get the last applicant response
    if applicant_entries:
        last_answer = applicant_entries[-1].get('content')
    
    # Try to determine last category from last question
    if last_question:
        last_category = detect_question_category_from_content(last_question)
    
    return {
        "is_resuming": session_resumed_flag and has_meaningful_content,  # FIXED
        "introduction_completed": introduction_completed,
        "introduction_asked": introduction_asked,
        "questions_asked": questions_asked,
        "last_category": last_category,
        "last_question": last_question,
        "last_answer": last_answer,
        "total_entries": len(transcript)
    }

def detect_question_category_from_content(question_text):
    """Detect category from question content"""
    if not question_text:
        return None
        
    question_lower = question_text.lower()
    
    category_keywords = {
        "personal": ["yourself", "background", "strengths", "weaknesses", "motivates", "achievement"],
        "study_plans": ["program", "university", "study", "research", "thesis", "academic", "courses"],
        "contributions": ["contribute", "indonesia", "development", "plans", "impact", "benefit"],
        "qualifications": ["academic", "achievements", "gpa", "skills", "experience", "research"],
        "leadership": ["leadership", "team", "organizations", "community", "initiative", "conflicts"],
        "knowledge_indonesia": ["indonesia", "challenges", "nationalism", "pancasila", "economic"]
    }
    
    for category, keywords in category_keywords.items():
        if any(keyword in question_lower for keyword in keywords):
            return category
    
    return None

class LPDPInterviewAgent(Agent):
    def __init__(self, room_metadata=None, user_metadata=None) -> None:
        global GLOBAL_TRANSCRIPT
        logger.info(f"=== RAW room_metadata ===")
        logger.info(f"Type: {type(room_metadata)}")
        logger.info(f"Content: {room_metadata}")
        # Parse room metadata to get interview configuration
        self.room_config = {}
        if room_metadata:
            try:
                self.room_config = json.loads(room_metadata)
                logger.info(f"Parsed room metadata: {self.room_config}")
            except json.JSONDecodeError as e:
                logger.error(f"Failed to parse room metadata: {e}")
                self.room_config = {}
        
        # Extract session resume information
        self.is_existing_session = self.room_config.get('session_resumed', False)
        session_id = self.room_config.get('sessionId', '')
        
        logger.info(f"AGENT INIT: session_resumed_flag={self.is_existing_session}")
        logger.info(f"AGENT INIT: session_id={session_id}")
        logger.info(f"AGENT INIT: current_transcript_length={len(GLOBAL_TRANSCRIPT)}")

        # Language Setting
        self.language = self.room_config.get('language', 'en')
        logger.info(f"Initializing interview agent with language: {self.language.upper()} ({'Bahasa Indonesia' if self.language == 'id' else 'English'})")
        self.resume_context = self.room_config.get('resume_context', {})
        
        logger.info(f"Session resume status: {self.is_existing_session}")
        if self.is_existing_session:
            logger.info(f"Resume context: {self.resume_context}")
        
        # Load existing transcript if this is a resumed session
        if self.is_existing_session and session_id:
            try:
                existing_transcript, existing_data = load_existing_transcript(session_id)
                if existing_transcript:
                    # Clear any existing entries to prevent duplication
                    GLOBAL_TRANSCRIPT.clear()
                    GLOBAL_TRANSCRIPT.extend(existing_transcript)
                    logger.info(f"Loaded {len(existing_transcript)} previous transcript entries (cleared duplicates)")
                    
                    # Get elapsed minutes from the loaded data
                    loaded_elapsed_minutes = existing_data.get('elapsed_minutes', 0)
                    if isinstance(loaded_elapsed_minutes, (int, float)) and loaded_elapsed_minutes > 0:
                        # Update resume context with accurate elapsed minutes
                        if 'resume_context' not in self.room_config:
                            self.room_config['resume_context'] = {}
                        self.room_config['resume_context']['elapsed_minutes'] = loaded_elapsed_minutes
                        self.resume_context = self.room_config['resume_context']
                        logger.info(f"Updated resume_context with elapsed_minutes: {loaded_elapsed_minutes:.1f}")
                    
                    # Try to calculate elapsed time if not found in data
                    if loaded_elapsed_minutes <= 0:
                        # Find earliest timestamp in transcript
                        earliest_time = None
                        latest_time = None
                        for entry in existing_transcript:
                            if entry.get("timestamp"):
                                try:
                                    entry_time = datetime.fromisoformat(entry.get("timestamp"))
                                    if earliest_time is None or entry_time < earliest_time:
                                        earliest_time = entry_time
                                    if latest_time is None or entry_time > latest_time:
                                        latest_time = entry_time
                                except:
                                    pass
                        
                        # Calculate elapsed minutes from timestamps
                        if earliest_time and latest_time:
                            calculated_elapsed = (latest_time - earliest_time).total_seconds() / 60
                            if calculated_elapsed > 0:
                                # Update resume context with calculated elapsed minutes
                                if 'resume_context' not in self.room_config:
                                    self.room_config['resume_context'] = {}
                                self.room_config['resume_context']['elapsed_minutes'] = calculated_elapsed
                                self.resume_context = self.room_config['resume_context']
                                logger.info(f"Calculated and updated elapsed_minutes: {calculated_elapsed:.1f}")
                    
                    # Analyze session state from transcript
                    self.previous_session_state = analyze_session_state_from_transcript(existing_transcript, self.room_config)
                    logger.info(f"Previous session state: {self.previous_session_state}")
                else:
                    self.previous_session_state = {"is_resuming": False}
            except Exception as e:
                logger.error(f"Error loading existing transcript: {e}")
                self.previous_session_state = {"is_resuming": False}
        else:
            logger.info("AGENT INIT: This is a NEW session - no transcript loading")
            self.previous_session_state = {"is_resuming": False}
        
        # Extract interview configuration
        interview_duration = self.room_config.get('duration', '01:00:00')
        interview_type = self.room_config.get('interview_type', 'full')
        category_name = self.room_config.get('category_name', 'LPDP')
        interview_name = self.room_config.get('name', 'LPDP Interview')
        
        # Extract sections from room config (new addition)
        sections = self.room_config.get('sections', [])
        logger.info(f"Extracted sections from room config: {sections}")
        
        # Parse duration to minutes
        self.duration_minutes = parse_duration_to_minutes(interview_duration)
        
        # Calculate adjusted duration for resumed sessions
        if self.is_existing_session and self.resume_context:
            elapsed_minutes = self.resume_context.get('elapsed_minutes', 0)
            if not isinstance(elapsed_minutes, (int, float)) or elapsed_minutes <= 0:
                # Fallback for invalid elapsed_minutes
                elapsed_minutes = 0
                logger.warning(f"Invalid elapsed_minutes in resume_context: {elapsed_minutes}")
            
            remaining_minutes = self.resume_context.get('remaining_minutes', self.duration_minutes)
            self.adjusted_duration = max(5, remaining_minutes)  # At least 5 minutes remaining
            logger.info(f"Adjusted duration for resumed session: {self.adjusted_duration} minutes (originally {self.duration_minutes})")
            logger.info(f"Session has elapsed {elapsed_minutes:.1f} minutes previously")
        else:
            self.adjusted_duration = self.duration_minutes
        
        logger.info(f"Interview duration set to: {self.adjusted_duration} minutes")
        
        # Extract user information from metadata or room config
        user_context = self.room_config.get('user_context', {})
        self.user_data = {
            "user_id": user_context.get('userId', 'anonymous'),
            "user_name": user_context.get('userName', 'Anonymous User'),
            "user_email": user_context.get('userEmail', ''),
            "interview_id": self.room_config.get('interviewId'),
            "category": self.room_config.get('category'),
            "type": self.room_config.get('type'),
            "session_start_time": datetime.now().isoformat()
        }
        
        # Override with direct user_metadata if provided
        if user_metadata:
            self.user_data.update({
                "user_name": user_metadata.get('userName', self.user_data['user_name']),
                "user_email": user_metadata.get('userEmail', self.user_data['user_email']),
                "user_id": user_metadata.get('userId', self.user_data['user_id']),
            })
        
        # Personalize instructions based on user data and room config
        user_name = self.user_data['user_name']
        first_name = user_name.split()[0] if user_name != "Anonymous User" else user_name
        
        # Get filtered questions based on interview type, category, AND sections (updated)
        self.questions = get_questions_for_interview_type(interview_type, category_name, sections)
        logger.info(f"Filtered question categories: {list(self.questions.keys())}")
        
        # Calculate estimated questions based on adjusted duration
        estimated_questions = max(5, int(self.adjusted_duration / 4))  # ~4 minutes per question
        
        # Create resume-aware instructions with explicit function tool usage
        resume_instruction = ""
        if self.is_existing_session:
            elapsed_min = self.resume_context.get('elapsed_minutes', 0)
            questions_asked = self.previous_session_state.get('questions_asked', 0)
            resume_instruction = f"""
            
            CRITICAL - SESSION RESUME MODE:
            This is a RESUMED session for {first_name}. Previous session data:
            - Conducted {elapsed_min:.1f} minutes of interview
            - Asked {questions_asked} questions previously
            - NEVER give welcome message again
            - NEVER ask introduction question again
            - IMMEDIATELY use continue_resume_session() function tool
            - Reference that you're continuing the session naturally
            """
        
        logger.info("=== INTERVIEW INSTRUCTIONS ===")
        logger.info(f"Language setting: {self.language} ({'Bahasa Indonesia' if self.language == 'id' else 'English'})")
        logger.info("Full instructions:")

        super().__init__(
            instructions=f"""
            Your name is Terra, an LPDP Interviewer with over 20 years of experience at Terang AI. You are designed to help users practice for their LPDP (Indonesia Endowment Fund for Education) scholarship interview.
            
            INTERVIEW CONFIGURATION:
            - Interview Name: {interview_name}
            - Duration: {interview_duration} (Adjusted: {self.adjusted_duration} minutes)
            - Type: {interview_type}
            - Category: {category_name}
            - Estimated Questions: {estimated_questions}
            - Session Type: {"RESUMED SESSION" if self.is_existing_session else "NEW SESSION"}
            - Available Question Categories: {list(self.questions.keys())}
            
            USER INFORMATION:
            - Name: {user_name}
            - Email: {self.user_data.get('user_email', 'N/A')}
            - Interview ID: {self.user_data.get('interview_id', 'N/A')}
            
            LANGUAGE RULES:
            - The interview will be conducted in {'Bahasa Indonesia' if self.language == 'id' else 'English'}
            - Always use {'Bahasa Indonesia' if self.language == 'id' else 'English'} for all your responses AND QUESTIONS
            - Maintain a professional but friendly tone
            - If the user responds in a different language, continue in the selected interview language
            - Use appropriate formal language and terms for a scholarship interview

            {resume_instruction}
            
            MANDATORY FUNCTION TOOL USAGE:
            You MUST use these function tools in the specified order and scenarios:
            
            CONVERSATION FLOW RULES:
            
            NEW SESSION FLOW:
            1. Give welcome message ending with "Ready to begin, {first_name}?" this first name is the highest precedence!
            2. When user responds → CALL handle_readiness_and_start_interview(user_response)
            → This asks for their introduction
            3. When user provides full introduction
            → calls ask_first_question() that provides contextual response + first interview question
            4. For each subsequent answer
            → calls get_next_question(answer)
            5. Continue until time completion → provide final summary
            
            RESUMED SESSION FLOW:
            1. Give brief welcome back message
            2. IMMEDIATELY CALL continue_resume_session() 
            3. For each subsequent answer
            → calls get_next_question(answer)
            4. Continue until time completion → provide final summary

            FOR RESUMED SESSIONS ONLY:
            1. IMMEDIATELY after welcome back message:
            → CALL continue_resume_session() to generate next question, and remember about the category and type
            
            FOR ALL SESSIONS (after introduction phase):
            1. For generating next questions:
            → CALL get_next_question(previous_answer) - this generates the next contextual question
            2. Alternative question generation:
            → CALL generate_contextual_question(previous_answer) for AI-powered questions
            
            CRITICAL FUNCTION TOOL REQUIREMENTS:
            - NEVER skip function tool calls - they are mandatory for proper operation
            - ALWAYS wait for function tool results before proceeding
            - Use EXACT function names: handle_readiness_and_start_interview, continue_resume_session, get_next_question, generate_contextual_question
            - Pass correct parameters to each function tool
            - Each function tool handles transcript logging automatically
            
            TRANSCRIPT HANDLING:
            - ALL user speech is automatically captured in transcript via speech events
            - Function tools should focus on generating appropriate responses

            LLM FORMAT:
            - Do not use markdown asterix symbol or etc, make every return is speakable.
            
            IMPORTANT: Trust the function tool flow. call ask_first_question(previous_answer) when appropriate. Do not manually try to determine conversation state.
            """,
        )
        logger.info(self.instructions)
        # Initialize session state based on resume status
        if self.is_existing_session and self.previous_session_state.get("is_resuming") and len(GLOBAL_TRANSCRIPT) > 0:
            self.session_state = {
                "introduction_completed": True,  # Always true for resumed sessions
                "introduction_asked": True,     # Always true for resumed sessions
                "questions_asked": self.previous_session_state.get("questions_asked", 0),
                "question_categories": list(self.questions.keys()),
                "last_category": self.previous_session_state.get("last_category"),
                "user_name": user_name,
                "first_name": first_name,
                "interview_complete": False,
                "transcript": GLOBAL_TRANSCRIPT,
                "start_time": datetime.now(),
                "estimated_questions": estimated_questions,
                "duration_minutes": self.adjusted_duration,
                "average_question_time": 4.0,
                "user_metadata": self.user_data,
                "room_config": self.room_config,
                "is_resumed_session": True,  # This is the key flag
                "last_answer": self.previous_session_state.get("last_answer"),
                "previous_context": self.previous_session_state,
                "resume_in_progress": True,  # Flag to prevent duplicate speech
                "original_transcript_length": len(GLOBAL_TRANSCRIPT),  # Track original size
                "available_sections": sections  # Store sections information
            }
            logger.info(f"Initialized RESUMED session state: {self.session_state['questions_asked']} questions asked previously")
            logger.info(f"RESUMED session with {len(GLOBAL_TRANSCRIPT)} transcript entries loaded")
        else:
            self.session_state = {
                "introduction_completed": False,
                "introduction_asked": False,
                "questions_asked": 0,
                "question_categories": list(self.questions.keys()),
                "last_category": None,
                "user_name": user_name,
                "first_name": first_name,
                "interview_complete": False,
                "transcript": GLOBAL_TRANSCRIPT,
                "start_time": datetime.now(),
                "estimated_questions": estimated_questions,
                "duration_minutes": self.adjusted_duration,
                "average_question_time": 4.0,
                "user_metadata": self.user_data,
                "room_config": self.room_config,
                "is_resumed_session": False,  # This is the key flag
                "last_answer": None,
                "previous_context": {},
                "resume_in_progress": False,
                "original_transcript_length": 0,
                "available_sections": sections  # Store sections information
            }
            logger.info("Initialized NEW session state")
            logger.info(f"NEW session with {len(GLOBAL_TRANSCRIPT)} transcript entries")
        
        # Initialize the save callback
        self.save_transcript_callback = None
        
        logger.info(f"Agent initialized for {interview_type} interview, duration: {self.adjusted_duration}min, categories: {list(self.questions.keys())}")

    def add_to_transcript(self, role, content, function):
        """Centralized method to add entries to transcript"""
        global GLOBAL_TRANSCRIPT
        
        entry = {
            "role": role,
            "content": content,
            "timestamp": datetime.now().isoformat(),
            "function": function
        }
        
        GLOBAL_TRANSCRIPT.append(entry)
        logger.info(f"Added to transcript - {role}: {content[:100]}...")
        logger.info(f"Transcript now has {len(GLOBAL_TRANSCRIPT)} entries")
        
        # Trigger immediate save after each transcript entry
        asyncio.create_task(self.save_transcript_immediately())
    
    async def save_transcript_immediately(self):
        """Save transcript immediately after each entry"""
        try:
            if self.save_transcript_callback:
                await self.save_transcript_callback()
        except Exception as e:
            logger.error(f"Error in immediate transcript save: {e}")

    async def on_enter(self):
        """Handle session entry - different behavior for new vs resumed sessions"""
        try:
            user_name = self.user_data['user_name']
            first_name = user_name.split()[0] if user_name != "Anonymous User" else user_name
            
            # FIXED: More explicit resume detection
            is_resumed = (
                self.is_existing_session and 
                self.room_config.get('session_resumed', False) and  # Explicit flag from room config
                len(GLOBAL_TRANSCRIPT) > 0 and  # Has actual transcript entries
                self.previous_session_state.get("is_resuming", False)
            )
            
            logger.info(f"RESUME DETECTION: is_existing_session={self.is_existing_session}")
            logger.info(f"RESUME DETECTION: session_resumed_flag={self.room_config.get('session_resumed', False)}")
            logger.info(f"RESUME DETECTION: transcript_length={len(GLOBAL_TRANSCRIPT)}")
            logger.info(f"RESUME DETECTION: is_resuming_flag={self.previous_session_state.get('is_resuming', False)}")
            logger.info(f"RESUME DETECTION: final_is_resumed={is_resumed}")
            
            if is_resumed:
                # Resumed session - brief welcome back 
                questions_asked = self.session_state["questions_asked"]
                
                # Get elapsed time with robust error handling
                elapsed_time = 0
                if self.resume_context and 'elapsed_minutes' in self.resume_context:
                    elapsed_time_raw = self.resume_context.get('elapsed_minutes', 0)
                    if isinstance(elapsed_time_raw, (int, float)) and elapsed_time_raw > 0:
                        elapsed_time = elapsed_time_raw
                    else:
                        logger.warning(f"Invalid elapsed_minutes in resume_context: {elapsed_time_raw}")
                
                # Fallback calculation for elapsed time if still zero
                if elapsed_time <= 0:
                    # Use a reasonable default based on average time per question
                    avg_time_per_question = 4  # 4 minutes per question
                    elapsed_time = questions_asked * avg_time_per_question
                    logger.info(f"No valid elapsed_minutes, using calculated time: {elapsed_time:.1f}")
                
                # Format elapsed time for better speech - LANGUAGE SPECIFIC
                if self.language == 'id':
                    elapsed_text = f"{int(elapsed_time)} menit" if int(elapsed_time) != 1 else "1 menit"
                    welcome_back_message = (
                        f"Selamat datang kembali, {first_name}! Saya Profesor Terra, dan kita akan melanjutkan sesi wawancara LPDP Anda. "
                        f"Kita telah membahas {questions_asked} pertanyaan dalam {elapsed_text} sebelumnya. "
                        f"Mari saya lanjutkan dengan pertanyaan berikutnya."
                    )
                else:
                    elapsed_text = f"{int(elapsed_time)} minute"
                    if int(elapsed_time) != 1:
                        elapsed_text += "s"
                    welcome_back_message = (
                        f"Welcome back, {first_name}! I'm Professor Terra, and we're continuing your LPDP interview session. "
                        f"We've covered {questions_asked} questions in our previous {elapsed_text} together. "
                        f"Let me continue with our next question."
                    )
                
                await self.session.say(welcome_back_message)
                # FIX: Add the welcome back message to transcript
                self.add_to_transcript("interviewer", welcome_back_message, 'on_enter_resumed')
                
                # Generate and speak the next question immediately
                logger.info("Generating continuation question for resumed session...")
                next_question = await self.continue_resume_session(context=None)
                
                # IMPORTANT: Actually speak the generated question
                await self.session.say(next_question)
                # FIX: Add the continuation question to transcript (this is already handled in continue_resume_session)
                logger.info(f"Spoke continuation question: {next_question[:100]}...")
                
                # Mark resume as completed
                self.session_state["resume_in_progress"] = False
                
            else:
                # New session - standard welcome
                # NEW SESSION - always execute this for fresh sessions
                logger.info("DETECTED: NEW SESSION - starting fresh interview")
                if not self.session_state["introduction_completed"]:
                    # Get interview details from room config
                    interview_name = self.room_config.get('name', 'LPDP Interview')
                    category_name = self.room_config.get('category_name', 'General')
                    interview_type = self.room_config.get('interview_type', 'Standard')
                    
                    # Format the duration for better speech output
                    duration_raw = self.room_config.get('duration', '01:00:00')
                    
                    # Parse the duration into a more speakable format with language support
                    def format_duration_for_speech(time_str, language='en'):
                        hours, minutes, seconds = time_str.split(':')
                        hours = int(hours)
                        minutes = int(minutes)
                        
                        if language == 'id':  # Indonesian
                            if hours > 0 and minutes > 0:
                                hour_text = f"{hours} jam"
                                minute_text = f"{minutes} menit"
                                return f"{hour_text} dan {minute_text}"
                            elif hours > 0:
                                return f"{hours} jam"
                            elif minutes > 0:
                                return f"{minutes} menit"
                            else:
                                return "sebentar"  # Fallback for very short durations
                        else:  # English (default)
                            if hours > 0 and minutes > 0:
                                return f"{hours} hour{'s' if hours > 1 else ''} and {minutes} minute{'s' if minutes > 1 else ''}"
                            elif hours > 0:
                                return f"{hours} hour{'s' if hours > 1 else ''}"
                            elif minutes > 0:
                                return f"{minutes} minute{'s' if minutes > 1 else ''}"
                            else:
                                return "a brief moment"  # Fallback for very short durations

                    def translate_section_names(section_names, language):
                        """Translate section names based on language"""
                        if language != 'id':
                            return section_names  # Return English names for non-Indonesian
                        
                        # Translation mapping for Indonesian
                        translation_map = {
                            "Personal Background": "Latar Belakang Pribadi",
                            "Study Plans": "Rencana Studi", 
                            "Future Contributions": "Kontribusi Masa Depan",
                            "Academic Qualifications": "Kualifikasi Akademik",
                            "Leadership Experience": "Pengalaman Kepemimpinan",
                            "Indonesia Knowledge": "Pengetahuan Indonesia",
                            "personal": "Latar Belakang Pribadi",
                            "study_plans": "Rencana Studi",
                            "contributions": "Kontribusi Masa Depan", 
                            "qualifications": "Kualifikasi Akademik",
                            "leadership": "Pengalaman Kepemimpinan",
                            "knowledge_indonesia": "Pengetahuan Indonesia"
                        }
                        
                        # Translate each section name
                        translated_names = []
                        for name in section_names:
                            translated_name = translation_map.get(name, name)  # Use original if no translation found
                            translated_names.append(translated_name)
                        
                        return translated_names

                    # Use the language-aware duration formatter
                    duration = format_duration_for_speech(duration_raw, self.language)
                    
                    # Get sections information for welcome message customization
                    sections = self.room_config.get('sections', [])
                    section_names = [section.get('name', '') for section in sections if 'name' in section]

                    # Translate section names if language is Indonesian
                    if section_names:
                        section_names = translate_section_names(section_names, self.language)

                    # Build additional section info for partial interviews
                    section_info = ""
                    if section_names and interview_type == "partial":
                        if self.language == 'id':
                            section_info = f" dengan fokus pada {', '.join(section_names)}"
                        else:
                            section_info = f" focusing on {', '.join(section_names)}"
                    
                    # LANGUAGE SPECIFIC welcome messages
                    if self.language == 'id':
                        welcome_message = (
                            f"Halo {user_name}, saya Profesor Terra, dan saya adalah pewawancara LPDP Anda untuk sesi hari ini di Terang AI. "
                            f"Selamat datang di latihan {interview_name} untuk kategori {category_name}{section_info}. "
                            f"Ini adalah sesi wawancara {interview_type} dengan durasi {duration} yang akan membantu Anda mempersiapkan proses seleksi beasiswa LPDP yang sebenarnya. "
                            f"Saya akan menanyakan beberapa pertanyaan relevan selama waktu yang dialokasikan. "
                            f"Mohon jawab dengan jelas dan percaya diri seperti dalam wawancara yang sesungguhnya. "
                            f"Siap untuk memulai, {first_name}?"
                        )
                    else:
                        welcome_message = (
                            f"Hello {user_name}, my name is Professor Terra, and I'm your LPDP interviewer for today's session at Terang AI. "
                            f"Welcome to this {interview_name} practice for the {category_name} category{section_info}. "
                            f"This is a {interview_type} interview session with a duration of {duration} that will help you prepare for the actual LPDP scholarship selection process. "
                            f"I'll be asking you several relevant questions over the allocated time. "
                            f"Please answer clearly and confidently as you would in a real interview. "
                            f"Ready to begin, {first_name}?"
                        )
                    
                    await self.session.say(welcome_message)
                    self.session_state["introduction_completed"] = True
                    
        except Exception as e:
            logger.error(f"Error in on_enter: {e}")
            logger.error(traceback.format_exc())

    @function_tool
    async def continue_resume_session(self, context: RunContext):
        """MANDATORY function tool for resumed sessions - generates appropriate next question"""
        try:
            first_name = self.session_state['first_name']
            
            # Get context from previous session
            previous_questions = self.session_state.get("questions_asked", 0)
            last_category = self.session_state.get("last_category")
            last_answer = self.session_state.get("last_answer", "")
            
            # FIXED: Get sections from room config - critical for category filtering
            sections = self.session_state.get("available_sections", [])
            section_ids = [section.get('id') for section in sections if 'id' in section]
            
            # FIXED: For partial interviews, strictly enforce section categories
            interview_type = self.room_config.get('interview_type', 'full')
            if interview_type == "partial" and section_ids:
                # Override available_categories with ONLY the categories from section_ids
                available_categories = section_ids
                logger.info(f"RESUME: Strict category enforcement: {section_ids}")
            else:
                # Fall back to all question categories if no section IDs
                available_categories = list(self.questions.keys())
            
            # Choose a different category from the last one if possible
            # BUT ONLY within available categories for partial interviews
            available_categories_filtered = [cat for cat in available_categories if cat != last_category]
            if not available_categories_filtered:
                available_categories_filtered = available_categories
            
            # Generate contextually appropriate question for resume - LANGUAGE SPECIFIC
            if self.language == 'id':
                resume_prompt = f"""Sebagai Terra, pewawancara LPDP, buatlah pertanyaan selanjutnya untuk sesi yang dilanjutkan dengan {first_name}:
                
                KONTEKS:
                - Ini adalah sesi wawancara yang DILANJUTKAN
                - Pertanyaan sebelumnya yang ditanyakan: {previous_questions}
                - Kategori terakhir yang dibahas: {last_category or "tidak diketahui"}
                - Kategori yang tersedia: {', '.join(available_categories_filtered)}
                - Respons terakhir tentang: "{last_answer if last_answer else "topik wawancara sebelumnya"}"
                
                PERSYARATAN:
                1. Pilih kategori yang berbeda dari {last_category} jika memungkinkan
                2. Buatlah pertanyaan yang menarik dan relevan dengan LPDP
                3. Jaga agar tetap percakapan dan profesional
                4. Jangan menyebut "melanjutkan" - langsung ajukan pertanyaan secara natural
                
                PENTING: Buatlah SATU pertanyaan wawancara yang jelas dalam Bahasa Indonesia."""
            else:
                resume_prompt = f"""As Terra, LPDP interviewer, generate next question for resumed session with {first_name}:
                
                CONTEXT:
                - This is a RESUMED interview session
                - Previous questions asked: {previous_questions}
                - Last category discussed: {last_category or "unknown"}
                - Available categories: {', '.join(available_categories_filtered)}
                - Last response was about: "{last_answer if last_answer else "previous interview topics"}"
                
                REQUIREMENTS:
                1. Choose different category from {last_category} if possible
                2. Generate engaging LPDP-relevant question
                3. Keep conversational and professional
                4. Don't mention "continuing" - just ask the question naturally
                
                Generate ONE clear interview question."""
            
            try:
                ai_question = await generate_ai_response(resume_prompt)
                
                # Clean up the question
                if ai_question.startswith('"') and ai_question.endswith('"'):
                    ai_question = ai_question[1:-1]
                
                # Detect category
                category_detected = detect_question_category_from_content(ai_question)
                
                # FIXED: Ensure the detected category is within available categories
                if category_detected and category_detected in available_categories:
                    self.session_state["last_category"] = category_detected
                elif available_categories:
                    # If detected category isn't valid, force to an available category
                    self.session_state["last_category"] = available_categories[0]
                
                # Update questions asked count
                self.session_state["questions_asked"] += 1
                
                # FIX: Add the continuation question to transcript
                self.add_to_transcript("interviewer", ai_question, "continue_resume_session")
                logger.info(f"Resume session: Generated continuation question for {first_name}")
                
                return ai_question
                
            except Exception as e:
                logger.error(f"Error generating resume question: {e}")
                # Fallback to predefined question - LANGUAGE SPECIFIC
                if available_categories_filtered:
                    category = random.choice(available_categories_filtered)
                    self.session_state["last_category"] = category
                    category_questions = self.questions.get(category, ["Tell me more about yourself."])
                    fallback_question = random.choice(category_questions)
                    
                    self.session_state["questions_asked"] += 1
                    # FIX: Add fallback question to transcript
                    self.add_to_transcript("interviewer", fallback_question, "continue_resume_session_fallback_1")
                    return fallback_question
                else:
                    # Ultimate fallback if no categories available - LANGUAGE SPECIFIC
                    if self.language == 'id':
                        fallback = f"Ceritakan tentang tantangan kepemimpinan yang pernah Anda hadapi dan bagaimana Anda mengatasinya."
                    else:
                        fallback = f"Tell me about a leadership challenge you've faced and how you overcame it."
                    
                    self.session_state["last_category"] = "leadership"
                    self.session_state["questions_asked"] += 1
                    # FIX: Add ultimate fallback to transcript
                    self.add_to_transcript("interviewer", fallback, "continue_resume_session_fallback_2")
                    return fallback
                    
        except Exception as e:
            logger.error(f"Error in continue_resume_session: {e}")
            first_name = self.session_state.get('first_name', 'there')
            
            # LANGUAGE SPECIFIC fallback
            if self.language == 'id':
                fallback = f"Ceritakan tentang tantangan kepemimpinan yang pernah Anda hadapi dan bagaimana Anda mengatasinya."
            else:
                fallback = f"Tell me about a leadership challenge you've faced and how you overcame it."
            
            # FIX: Add error fallback to transcript
            self.add_to_transcript("interviewer", fallback, "continue_resume_session_fallback_0")
            return fallback

    # Keeping only the essential methods, skipping the rest for brevity
    @function_tool
    async def handle_readiness_and_start_interview(self, context: RunContext, user_response: str):
        """Handle the user's response to 'Ready to begin?' and immediately start the interview."""
        
        # Only for new sessions
        if self.session_state["is_resumed_session"]:
            return "Session already in progress."
        
        first_name = self.session_state['first_name']
        
        # Combine acknowledgment with introduction question for smoother flow
        if self.language == 'id':
            combined_response = (
                f"Baik, {first_name}! Mari mulai. "
                f"Mari kita awali dengan perkenalan diri Anda, beritahu saya tentang diri Anda dan latar belakang Anda."
            )
        else:
            combined_response = (
                f"Great, {first_name}! Let's begin. "
                f"Let's start with your introduction, please tell me about yourself and your background."
            )
        
        # Mark that we've asked the introduction (but don't count it as a formal interview question yet)
        self.session_state["introduction_asked"] = True
        
        return combined_response

    @function_tool
    async def ask_first_question(self, context: RunContext, previous_answer: str):
        """Called after the user responds to the introduction to ask the first actual interview question."""
        
        # Only for new sessions
        if self.session_state["is_resumed_session"]:
            return await self.generate_contextual_question(context, "")
        
        first_name = self.session_state['first_name']
        
        # Get the user's introduction from the transcript (look for the most recent applicant entry)
        user_introduction = ""
        applicant_entries = [entry for entry in GLOBAL_TRANSCRIPT if entry.get('role') == 'applicant']
        
        if applicant_entries:
            # Get the last applicant entry (which should be their introduction)
            last_entry = applicant_entries[-1]
            user_introduction = last_entry.get('content', '')
            logger.info(f"Found user introduction: {user_introduction[:100]}...")
        else:
            logger.warning("No applicant entries found in transcript")
            user_introduction = ""
        
        # Only proceed if we have a meaningful introduction (more than just "yes" or short responses)
        if len(user_introduction) < 10:
            logger.warning(f"Introduction too short ({len(user_introduction)} chars): '{user_introduction}'")
            # Ask for a proper introduction - LANGUAGE SPECIFIC
            if self.language == 'id':
                request_proper_intro = f"Saya ingin mendengar lebih banyak tentang Anda, {first_name}. Bisakah Anda memberikan perkenalan yang lebih detail tentang latar belakang, pendidikan, dan apa yang membuat Anda tertarik mengajukan beasiswa LPDP?"
            else:
                request_proper_intro = f"I'd like to hear more about you, {first_name}. Could you please give me a more detailed introduction about your background, education, and what brings you to apply for the LPDP scholarship?"
            
            return request_proper_intro
        
        # Generate AI-powered contextual response to their introduction - LANGUAGE SPECIFIC
        if self.language == 'id':
            intro_response_prompt = f"""Sebagai Terra, pewawancara LPDP, berikan respons kontekstual singkat 1 kalimat untuk perkenalan {first_name}:
            
            PERKENALAN: "{user_introduction}"
            
            PERSYARATAN:
            1. Akui sesuatu yang spesifik dari latar belakang mereka
            2. Hubungkan dengan tujuan beasiswa LPDP atau potensi
            3. Terdengar natural dan mendorong
            4. Tepat 1 kalimat dalam Bahasa Indonesia
            
            Contoh format:
            - "Saya bisa melihat latar belakang Anda di [bidang] dan pengalaman dengan [hal spesifik] sejalan dengan misi LPDP untuk mengembangkan pemimpin masa depan, {first_name}."
            - "Perjalanan Anda dari [detail latar belakang] ke [fokus saat ini] menunjukkan jenis pemikiran strategis yang dihargai LPDP pada penerima beasiswa, {first_name}."
            
            Buatlah SATU kalimat kontekstual dalam Bahasa Indonesia yang merespons perkenalan mereka."""
        else:
            intro_response_prompt = f"""As Terra, LPDP interviewer, provide a brief 1-sentence contextual response to {first_name}'s introduction:
            
            INTRODUCTION: "{user_introduction}"
            
            REQUIREMENTS:
            1. Acknowledge something specific from their background
            2. Connect it to LPDP scholarship goals or potential
            3. Sound natural and encouraging
            4. Keep it exactly 1 sentence
            
            Example formats:
            - "I can see your background in [field] and experience with [specific thing] aligns well with LPDP's mission to develop future leaders, {first_name}."
            - "Your journey from [background detail] to [current focus] demonstrates the kind of strategic thinking LPDP values in scholarship recipients, {first_name}."
            
            Generate ONE contextual sentence responding to their introduction."""
        
        try:
            intro_response = await generate_ai_response(intro_response_prompt)
            
            # Clean up the response
            if intro_response.startswith('"') and intro_response.endswith('"'):
                intro_response = intro_response[1:-1]
            
            # Ensure it addresses the user personally if it doesn't already
            if not intro_response.lower().__contains__(first_name.lower()):
                intro_response = intro_response.rstrip('.') + f", {first_name}."
                
        except Exception as e:
            logger.error(f"Error generating contextual intro response: {e}")
            # Fallback response - LANGUAGE SPECIFIC
            if self.language == 'id':
                intro_response = f"Terima kasih atas perkenalan yang komprehensif, {first_name}. Saya bisa melihat Anda memiliki fondasi yang kuat untuk kesempatan beasiswa ini."
            else:
                intro_response = f"Thank you for that comprehensive introduction, {first_name}. I can see you have a strong foundation for this scholarship opportunity."
        
        # Now generate the first contextual interview question
        available_categories = list(self.questions.keys())
        first_category = available_categories[0] if available_categories else "personal"
        
        # Get a question from the selected category and make it contextual
        category_questions = self.questions.get(first_category, ['What motivates you to pursue higher education?'])
        base_question = random.choice(category_questions)
        
        # Add contextual background to the question - LANGUAGE SPECIFIC
        contextual_question = await self._add_context_to_question(base_question, first_category, first_name, user_introduction)
        
        # Combine the introduction response with the contextual first question - LANGUAGE SPECIFIC
        if self.language == 'id':
            full_response = f"{intro_response}\n\nSekarang mari kita lanjut ke pertanyaan wawancara pertama. {contextual_question}"
        else:
            full_response = f"{intro_response}\n\nNow let's move to our first interview question. {contextual_question}"
        
        # Update session state - now we count this as the first actual interview question
        self.session_state["questions_asked"] = 1
        self.session_state["last_category"] = first_category
        
        logger.info(f"Generated first interview question for {first_name} after proper introduction")
        
        return full_response

    async def _add_context_to_question(self, base_question: str, category: str, first_name: str, previous_answer: str = "") -> str:
        """Add contextual background to predefined questions"""
        
        # LANGUAGE SPECIFIC category contexts
        if self.language == 'id':
            category_contexts = {
                "personal": f"Memahami latar belakang pribadi dan karakter Anda sangat penting untuk evaluasi LPDP, karena beasiswa ini mencari individu dengan fondasi pribadi yang kuat dan kesadaran diri yang jelas. Kualitas pribadi Anda akan menentukan seberapa efektif Anda dapat mewakili Indonesia di luar negeri dan berkontribusi setelah kembali.",
                
                "study_plans": f"LPDP berinvestasi pada pelajar yang telah meneliti perjalanan akademik mereka secara menyeluruh dan dapat menunjukkan pemikiran strategis tentang pendidikan mereka. Rencana studi Anda menunjukkan tingkat komitmen dan persiapan untuk kesuksesan akademik di luar negeri.",
                
                "contributions": f"Tujuan utama beasiswa LPDP adalah mengembangkan pemimpin masa depan yang akan mendorong kemajuan Indonesia. Rencana kontribusi Anda sangat penting untuk mengevaluasi bagaimana pendidikan Anda akan menguntungkan pembangunan bangsa.",
                
                "qualifications": f"LPDP mencari kandidat yang unggul secara akademik yang dapat berhasil menyelesaikan program kelas dunia dan membawa pengetahuan berharga kembali ke Indonesia. Kualifikasi Anda menunjukkan kesiapan untuk tantangan akademik di luar negeri.",
                
                "leadership": f"Kemampuan kepemimpinan adalah kompetensi inti yang dicari LPDP pada penerima beasiswa, karena pelajar masa depan diharapkan mendorong perubahan positif di Indonesia. Pengalaman kepemimpinan Anda menunjukkan potensi untuk membuat dampak yang bermakna.",
                
                "knowledge_indonesia": f"Sebagai pelajar LPDP, Anda akan menjadi duta Indonesia saat belajar di luar negeri dan kontributor kunci untuk pembangunan nasional setelah kembali. Pemahaman Anda tentang tantangan dan peluang Indonesia sangat vital untuk peran ini."
            }
        else:
            category_contexts = {
                "personal": f"Understanding your personal background and character is crucial for LPDP evaluation, as the scholarship seeks individuals with strong personal foundations and clear self-awareness. Your personal qualities will determine how effectively you can represent Indonesia abroad and contribute upon your return.",
                
                "study_plans": f"LPDP invests in scholars who have thoroughly researched their academic journey and can demonstrate strategic thinking about their education. Your study plans reveal your commitment level and preparation for academic success abroad.",
                
                "contributions": f"The ultimate goal of LPDP scholarship is to develop future leaders who will drive Indonesia's progress. Your contribution plans are essential to evaluate how your education will benefit our nation's development.",
                
                "qualifications": f"LPDP seeks academically excellent candidates who can successfully complete world-class programs and bring valuable knowledge back to Indonesia. Your qualifications demonstrate your readiness for academic challenges abroad.",
                
                "leadership": f"Leadership capability is a core competency LPDP looks for in scholarship recipients, as future scholars are expected to drive positive change in Indonesia. Your leadership experiences show your potential to make meaningful impact.",
                
                "knowledge_indonesia": f"As an LPDP scholar, you'll be an ambassador for Indonesia while studying abroad and a key contributor to national development upon return. Your understanding of Indonesia's challenges and opportunities is vital for this role."
            }
        
        context = category_contexts.get(category, 
            "Pertanyaan ini membantu kami memahami perspektif dan persiapan Anda untuk program beasiswa LPDP." if self.language == 'id' 
            else "This question helps us understand your perspective and preparation for the LPDP scholarship program."
        )
        
        # Add reference to their introduction if meaningful
        connection = ""
        if previous_answer and len(previous_answer) > 50:
            # Generate a brief connection to their introduction - LANGUAGE SPECIFIC
            if self.language == 'id':
                connection_prompt = f"""Berdasarkan perkenalan ini: "{previous_answer}", buatlah frasa penghubung singkat (5-10 kata) yang menghubungkan ke pertanyaan {category}.
                
                Contoh:
                - "Berdasarkan latar belakang teknik Anda,"
                - "Mengingat keterlibatan komunitas Anda,"
                - "Mempertimbangkan perjalanan akademik Anda,"
                
                Buatlah frasa penghubung singkat dalam Bahasa Indonesia:"""
            else:
                connection_prompt = f"""Based on this introduction: "{previous_answer}", create a brief connecting phrase (5-10 words) that links to a {category} question.
                
                Examples:
                - "Building on your engineering background,"
                - "Given your community involvement,"
                - "Considering your academic journey,"
                
                Generate a brief connecting phrase:"""
            
            try:
                connection_phrase = await generate_ai_response(connection_prompt)
                if connection_phrase and len(connection_phrase) < 50:
                    connection = f" {connection_phrase.strip()} "
            except:
                connection = " Berdasarkan apa yang Anda sampaikan, " if self.language == 'id' else " Building on what you shared, "
        
        return f"{context}{connection}{base_question.lower()}"

    @function_tool
    async def get_next_question(self, context: RunContext, previous_answer: str = ""):
        """Generate AI-powered interview questions based on context and previous responses."""
        return await self.generate_contextual_question(context, previous_answer)

    @function_tool
    async def generate_contextual_question(self, context: RunContext, previous_answer: str = ""):
        """Generate AI-powered follow-up questions based on the user's previous response."""
        # Get the last answer from session state if not provided
        if not previous_answer:
            previous_answer = self.session_state.get("last_answer", "")
        
        # FIXED TIME CALCULATION LOGIC
        elapsed_minutes = (datetime.now() - self.session_state["start_time"]).total_seconds() / 60
        total_duration = self.session_state["duration_minutes"]  # Use the adjusted duration
        remaining_minutes = total_duration - elapsed_minutes
        
        # DEBUG LOGGING - Add this to see what's happening
        logger.info(f"TIME DEBUG: elapsed={elapsed_minutes:.1f}, total_duration={total_duration:.1f}, remaining={remaining_minutes:.1f}")
        
        if self.session_state["questions_asked"] > 0:
            self.session_state["average_question_time"] = elapsed_minutes / self.session_state["questions_asked"]
        
        # FIXED: More conservative end-of-interview detection
        estimated_remaining_questions = int(remaining_minutes / self.session_state["average_question_time"]) if self.session_state["average_question_time"] > 0 else 5
        
        # DEBUG LOGGING
        logger.info(f"QUESTIONS DEBUG: asked={self.session_state['questions_asked']}, avg_time={self.session_state['average_question_time']:.1f}, estimated_remaining={estimated_remaining_questions}")
        
        # Prepare context for AI question generation
        first_name = self.session_state['first_name']
        
        # Format time values for speech
        elapsed_text = f"{int(elapsed_minutes)} minute{'s' if int(elapsed_minutes) != 1 else ''}"
        remaining_text = f"{int(remaining_minutes)} minute{'s' if int(remaining_minutes) != 1 else ''}"
        
        # Check if this is a partial interview with limited categories
        interview_type = self.room_config.get('interview_type', 'full')
        
        # Get sections from room config - critical for category filtering
        sections = self.session_state.get("available_sections", [])
        section_ids = [section.get('id') for section in sections if 'id' in section]
        
        # FIXED: Ensure we respect section IDs from room config for both new and resumed sessions
        if interview_type == "partial" and section_ids:
            # Override available_categories with ONLY the categories from section_ids
            available_categories = section_ids
            logger.info(f"STRICT CATEGORY ENFORCEMENT: Limiting to section IDs: {section_ids}")
        else:
            # Fall back to all question categories if no section IDs
            available_categories = list(self.questions.keys())
        
        # Enhanced context for resumed sessions
        session_context = ""
        if self.session_state["is_resumed_session"]:
            total_questions = self.session_state["questions_asked"]
            session_context = f"This is a resumed session. Total questions asked so far: {total_questions}. "
        
        # FIX: Add language-specific prompts
        if self.language == 'id':
            # Indonesian prompts
            if interview_type == "partial" and len(available_categories) <= 2:
                category = available_categories[0]
                category_questions = self.questions.get(category, ["Tell me more about yourself."])
                
                if random.random() < 0.5:
                    ai_question = random.choice(category_questions)
                    logger.info(f"Using predefined {category} question for partial interview")
                else:
                    question_prompt = f"""Sebagai Terra, pewawancara LPDP, buatlah pertanyaan {category} untuk {first_name}:
                    
                    KONTEKS:
                    - Ini adalah wawancara parsial yang fokus HANYA pada topik {category}
                    - {session_context}
                    - Pertanyaan yang sudah ditanyakan: {self.session_state["questions_asked"]}
                    - Waktu: {elapsed_text} telah berlalu, {remaining_text} tersisa
                    - Jawaban sebelumnya: "{previous_answer}..."
                    
                    PERSYARATAN KETAT: Buatlah HANYA pertanyaan yang berkaitan dengan {category}.
                    
                    Untuk kategori {category}, fokus pada:
                    {self._get_category_focus_indonesian(category)}

                    PENTING: JANGAN sebutkan nama kategori dalam respons Anda. Ajukan pertanyaan secara natural tanpa mengatakan "Ini tentang {category}" atau "Kategori: {category}".
                    
                    Buatlah SATU pertanyaan wawancara {category} yang jelas dengan kalimat tambahan yang personal."""
                    
                    try:
                        ai_question = await generate_ai_response(question_prompt)
                        if ai_question.startswith('"') and ai_question.endswith('"'):
                            ai_question = ai_question[1:-1]
                        logger.info(f"Generated AI {category} question for partial interview in Indonesian")
                    except Exception as e:
                        logger.error(f"AI generation failed, using predefined {category} question: {e}")
                        ai_question = random.choice(category_questions)
                
                self.session_state["last_category"] = category
            else:
                # Full interview in Indonesian
                question_prompt = f"""Sebagai Terra, pewawancara LPDP, buatlah pertanyaan selanjutnya untuk {first_name}:
                
                KONTEKS:
                - {session_context}
                - Pertanyaan yang ditanyakan sesi ini: {self.session_state["questions_asked"]}
                - Waktu: {elapsed_text} telah berlalu, {remaining_text} tersisa
                - Jawaban sebelumnya: "{previous_answer}..."
                - Kategori terakhir: {self.session_state.get("last_category", "tidak ada")}
                - Tersedia: {', '.join(available_categories)}
                
                ATURAN:
                1. Pilih kategori yang berbeda dari yang terakhir jika memungkinkan
                2. Relevan dengan tujuan beasiswa LPDP
                3. Bangun dari respons sebelumnya
                4. Jaga agar tetap jelas dan fokus
                
                Buatlah SATU pertanyaan wawancara yang jelas dalam Bahasa Indonesia."""
                
                try:
                    ai_question = await generate_ai_response(question_prompt)
                    if ai_question.startswith('"') and ai_question.endswith('"'):
                        ai_question = ai_question[1:-1]
                    
                    category_detected = detect_question_category_from_content(ai_question)
                    
                    if category_detected and category_detected in available_categories:
                        self.session_state["last_category"] = category_detected
                    elif available_categories:
                        self.session_state["last_category"] = available_categories[0]
                        
                except Exception as e:
                    logger.error(f"Error generating AI question: {e}")
                    if available_categories:
                        category = random.choice(available_categories)
                        self.session_state["last_category"] = category
                        category_questions = self.questions.get(category, ["Ceritakan lebih lanjut tentang diri Anda."])
                        ai_question = random.choice(category_questions)
                    else:
                        ai_question = "Ceritakan tentang tantangan kepemimpinan yang pernah Anda hadapi dan bagaimana Anda mengatasinya."
                        self.session_state["last_category"] = "leadership"
        else:
            # English prompts (existing logic)
            if interview_type == "partial" and len(available_categories) <= 2:
                category = available_categories[0]
                category_questions = self.questions.get(category, ["Tell me more about yourself."])
                
                if random.random() < 0.5:
                    ai_question = random.choice(category_questions)
                    logger.info(f"Using predefined {category} question for partial interview")
                else:
                    question_prompt = f"""As Terra, LPDP interviewer, generate a {category} question for {first_name}:
                    
                    CONTEXT:
                    - This is a partial interview focused ONLY on {category} topics
                    - {session_context}
                    - Questions asked: {self.session_state["questions_asked"]}
                    - Time: {elapsed_text} elapsed, {remaining_text} remaining
                    - Previous answer: "{previous_answer}..."
                    
                    STRICT REQUIREMENT: Generate ONLY a {category}-related question.
                    
                    For {category} category, focus on:
                    {self._get_category_focus(category)}

                    CRITICAL: DO NOT mention the category name in your response. Ask the question naturally without saying "This is about {category}" or "Category: {category}".
                    
                    Generate ONE clear {category} interview question with additional personalized sentences."""
                    
                    try:
                        ai_question = await generate_ai_response(question_prompt)
                        if ai_question.startswith('"') and ai_question.endswith('"'):
                            ai_question = ai_question[1:-1]
                        logger.info(f"Generated AI {category} question for partial interview")
                    except Exception as e:
                        logger.error(f"AI generation failed, using predefined {category} question: {e}")
                        ai_question = random.choice(category_questions)
                
                self.session_state["last_category"] = category
            else:
                # Full interview in English
                question_prompt = f"""As Terra, LPDP interviewer, generate next question for {first_name}:
                
                CONTEXT:
                - {session_context}
                - Questions asked this session: {self.session_state["questions_asked"]}
                - Time: {elapsed_text} elapsed, {remaining_text} remaining
                - Previous answer: "{previous_answer}..."
                - Last category: {self.session_state.get("last_category", "none")}
                - Available: {', '.join(available_categories)}
                
                RULES:
                1. Choose different category from last one if possible
                2. Relevant to LPDP scholarship goals
                3. Build on previous responses
                4. Keep clear and focused
                
                Generate ONE clear interview question."""
                
                try:
                    ai_question = await generate_ai_response(question_prompt)
                    if ai_question.startswith('"') and ai_question.endswith('"'):
                        ai_question = ai_question[1:-1]
                    
                    category_detected = detect_question_category_from_content(ai_question)
                    
                    if category_detected and category_detected in available_categories:
                        self.session_state["last_category"] = category_detected
                    elif available_categories:
                        self.session_state["last_category"] = available_categories[0]
                        
                except Exception as e:
                    logger.error(f"Error generating AI question: {e}")
                    if available_categories:
                        category = random.choice(available_categories)
                        self.session_state["last_category"] = category
                        category_questions = self.questions.get(category, ["Tell me more about yourself."])
                        ai_question = random.choice(category_questions)
                    else:
                        ai_question = "Tell me about a leadership challenge you've faced and how you overcame it."
                        self.session_state["last_category"] = "leadership"
        
        # Update session state
        self.session_state["questions_asked"] += 1
        
        # FIX: Language-specific end-of-interview messages
        if remaining_minutes <= 2:
            if self.language == 'id':
                ai_question += f"\n\nIni akan menjadi pertanyaan terakhir kita, {first_name}, karena kita mendekati akhir sesi wawancara."
            else:
                ai_question += f"\n\nThis will be our final question, {first_name}, as we're approaching the end of our session."
            self.session_state["interview_complete"] = True
            logger.info(f"FINAL QUESTION: Set interview_complete=True with {remaining_minutes:.1f} minutes remaining")
        elif remaining_minutes <= 5 and estimated_remaining_questions <= 2:
            if self.language == 'id':
                ai_question += f"\n\nKita mendekati akhir sesi wawancara, {first_name}. Saya masih punya beberapa pertanyaan untuk Anda."
            else:
                ai_question += f"\n\nWe're nearing the end of our interview session, {first_name}. I have a few more questions for you."
            logger.info(f"NEARING END: {remaining_minutes:.1f} minutes remaining, {estimated_remaining_questions} questions estimated")
        
        # Enhanced logging
        user_name = self.user_data.get('user_name', 'unknown')
        user_email = self.user_data.get('user_email', 'unknown')
        session_type = "RESUMED" if self.session_state["is_resumed_session"] else "NEW"
        logger.info(f"AI-generated question for {user_name} (Email: {user_email}) [{session_type}]: "
                    f"{self.session_state['questions_asked']} questions, "
                    f"{elapsed_minutes:.1f} min elapsed, {remaining_minutes:.1f} min remaining")
        
        return ai_question

    def _get_category_focus(self, category: str) -> str:
        """Get focus description for each category"""
        focus_map = {
            "personal": "personal background, strengths, weaknesses, motivations, achievements, goals, values, challenges overcome",
            "study_plans": "university choice, program selection, academic preparation, research plans, study abroad reasons",
            "contributions": "future contributions to Indonesia, development plans, knowledge application, impact goals",
            "qualifications": "academic achievements, GPA, research experience, skills, certifications, technical abilities", 
            "leadership": "leadership experiences, team management, community service, organizational involvement, conflict resolution",
            "knowledge_indonesia": "Indonesia's challenges, nationalism, Pancasila, economic conditions, infrastructure, diversity"
        }
        return focus_map.get(category, "general interview topics")
    
    def _get_category_focus_indonesian(self, category: str) -> str:
        """Get focus description for each category in Indonesian"""
        focus_map = {
            "personal": "latar belakang pribadi, kekuatan, kelemahan, motivasi, pencapaian, tujuan, nilai-nilai, tantangan yang dihadapi",
            "study_plans": "pilihan universitas, seleksi program, persiapan akademik, rencana penelitian, alasan studi ke luar negeri",
            "contributions": "kontribusi masa depan untuk Indonesia, rencana pengembangan, aplikasi pengetahuan, tujuan dampak",
            "qualifications": "pencapaian akademik, IPK, pengalaman penelitian, keterampilan, sertifikasi, kemampuan teknis", 
            "leadership": "pengalaman kepemimpinan, manajemen tim, pelayanan masyarakat, keterlibatan organisasi, resolusi konflik",
            "knowledge_indonesia": "tantangan Indonesia, nasionalisme, Pancasila, kondisi ekonomi, infrastruktur, keberagaman"
        }
        return focus_map.get(category, "topik wawancara umum")

    @function_tool
    async def analyze_answer_depth(self, context: RunContext, answer: str):
        """Analyze the depth and quality of the user's answer using AI."""
        
        # Optimized shorter prompt for faster analysis
        analysis_prompt = f"""Analyze this LPDP candidate's answer briefly:
        
        ANSWER: "{answer}"
        
        Rate (1-3 scale):
        1. Content depth 
        2. Specific examples
        3. LPDP relevance
        4. Structure/clarity
        5. Indonesia connection
        6. Preparation level
        
        Brief analysis (1 sentences max)."""
        
        # Use direct Google AI API
        return await generate_ai_response(analysis_prompt)

    @function_tool
    async def generate_follow_up_question(self, context: RunContext, original_answer: str, topic: str):
        """Generate intelligent follow-up questions to dive deeper into specific topics."""
        
        first_name = self.session_state['first_name']
        
        # Optimized shorter prompt
        follow_up_prompt = f"""As Terra, create follow-up question for {first_name} about {topic}:
        
        ANSWER: "{original_answer}"
        
        Generate ONE question that:
        1. Asks for specific examples
        2. Connects to LPDP goals
        3. Is natural and conversational
        4. Please make the text speakable for conversation purpose, do not use markdown or any formatting
        """
        
        # Use direct Google AI API
        result = await generate_ai_response(follow_up_prompt)
        return result if result else f"Could you elaborate more on that point, {first_name}?"


def prewarm(proc: JobProcess):
    proc.userdata["vad"] = silero.VAD.load()

def extract_session_id_from_metadata(room_config, user_metadata, timestamp):
    """Extract session ID from room metadata or create new one"""
    
    # Priority 1: Use sessionId from room config (from frontend)
    if room_config.get('sessionId'):
        session_id = room_config['sessionId']
        logger.info(f"Using session ID from room config: {session_id}")
        return session_id
    
    # Priority 2: Check user_context for sessionId
    user_context = room_config.get('user_context', {})
    if user_context.get('sessionId'):
        session_id = user_context['sessionId']
        logger.info(f"Using session ID from user context: {session_id}")
        return session_id
    
    # Priority 3: Create new session ID
    user_email = ""
    if user_metadata and user_metadata.get('userEmail'):
        user_email = user_metadata['userEmail']
    elif user_context.get('userEmail'):
        user_email = user_context['userEmail']
    
    if user_email:
        email_safe = user_email.replace('@', '_').replace('.', '_')
        session_id = f"session_{email_safe}_{timestamp}"
    else:
        session_id = f"session_{timestamp}_{int(time.time())}"
    
    logger.info(f"Created new session ID: {session_id}")
    return session_id


async def entrypoint(ctx: JobContext):
    global TRANSCRIPT_FILENAME, TEXT_TRANSCRIPT_FILENAME, GLOBAL_TRANSCRIPT
    
    # Extract room metadata
    room_metadata = None
    user_metadata = None
    
    await ctx.connect()
    
    # Extract room metadata from the connected room
    if ctx.room.metadata:
        room_metadata = ctx.room.metadata
        logger.info(f"Room metadata found: {room_metadata}")
    else:
        logger.info("No room metadata found")
    
    # Parse room metadata to check for session resume
    room_config = {}
    if room_metadata:
        try:
            room_config = json.loads(room_metadata)
            is_resumed = room_config.get('session_resumed', False)
            
            # DEBUG: Log the full room config
            logger.info(f"=== ROOM METADATA DEBUG ===")
            logger.info(f"Full room_config: {room_config}")
            logger.info(f"sessionId in room_config: {room_config.get('sessionId')}")
            logger.info(f"user_context: {room_config.get('user_context', {})}")
            logger.info(f"session_resumed flag: {room_config.get('session_resumed')}")
            
            # FIXED: More explicit transcript handling
            if is_resumed:
                logger.info("ENTRYPOINT: This is a RESUMED session - keeping existing transcript")
                # Don't clear transcript for resumed sessions
            else:
                # CLEAR transcript for new sessions - CRITICAL FIX
                GLOBAL_TRANSCRIPT.clear()  # Use clear() instead of assignment
                logger.info("ENTRYPOINT: This is a NEW session - cleared transcript")
        except Exception as e:
            logger.error(f"Error parsing room metadata: {e}")
            GLOBAL_TRANSCRIPT.clear()  # Clear on error
            logger.info("ENTRYPOINT: Could not parse room metadata - treating as new session")
    else:
        GLOBAL_TRANSCRIPT.clear()  # Clear when no room metadata
        logger.info("ENTRYPOINT: No room metadata - treating as new session")
    
    logger.info(f"ENTRYPOINT: Final transcript length: {len(GLOBAL_TRANSCRIPT)}")
    
    # VALIDATED APPROACH: Extract user metadata from room participants
    def extract_user_metadata():
        nonlocal user_metadata
        try:
            participants = list(ctx.room.remote_participants.values())
            if participants:
                participant = participants[0]
                if participant.metadata:
                    user_metadata = json.loads(participant.metadata)
                    logger.info(f"Successfully extracted user metadata: {user_metadata}")
                else:
                    logger.info("No participant metadata found in token")
            else:
                logger.info("No remote participants found yet")
        except Exception as e:
            logger.error(f"Error extracting user metadata: {e}")
    
    # Event handler for when participants connect
    @ctx.room.on("participant_connected")
    def on_participant_connected(participant):
        nonlocal user_metadata
        try:
            if participant.metadata:
                user_metadata = json.loads(participant.metadata)
                logger.info(f"User metadata from connected participant: {user_metadata}")
        except Exception as e:
            logger.error(f"Error parsing participant metadata: {e}")
    
    # Wait for participant and try to get metadata
    logger.info("Waiting for participant to join...")
    
    try:
        # Add timeout to prevent hanging
        await asyncio.wait_for(ctx.wait_for_participant(), timeout=30.0)
        logger.info("Participant joined successfully")
    except asyncio.TimeoutError:
        logger.error("Timeout waiting for participant to join")
        return
    except Exception as e:
        logger.error(f"Error waiting for participant: {e}")
        return
    
    # Try to extract metadata
    extract_user_metadata()
    
    # FIXED: Extract session ID properly from room metadata
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    session_id = extract_session_id_from_metadata(room_config, user_metadata, timestamp)
    
    # Determine user info with better fallback logic
    user_context = room_config.get('user_context', {})
    user_email = (user_metadata and user_metadata.get('userEmail')) or user_context.get('userEmail', 'unknown')
    user_name = (user_metadata and user_metadata.get('userName')) or user_context.get('userName', user_email)
    interview_id = room_config.get('interviewId', 'default')
    
    # Set transcript filenames using the EXTRACTED session ID
    TRANSCRIPT_FILENAME = os.path.join(TRANSCRIPTS_DIR, f"{session_id}.json")
    TEXT_TRANSCRIPT_FILENAME = os.path.join(TRANSCRIPTS_DIR, f"{session_id}.txt")
    
    session_type = "RESUMED" if room_config.get('session_resumed') else "NEW"
    logger.info(f"{session_type} Session for user: {user_name} ({user_email}), Interview: {interview_id}")
    logger.info(f"Using session ID: {session_id}")
    logger.info(f"Room config: {room_config}")
    logger.info(f"Will save transcript to: {TRANSCRIPT_FILENAME}")
    
    # Set up LiveKit recording with the EXTRACTED session ID
    egress_id = None
    recording_path = None
    try:
        # Start LiveKit Egress recording
        bucket_name = "terang-ai-assets"  # Your GCS bucket
        region = "asia-southeast2"  # Jakarta region
        
        # Update to get both egress_id and recording_path
        egress_id, recording_path = await setup_livekit_recording(
            ctx=ctx,
            session_id=session_id,  # Use the extracted session_id
            bucket_name=bucket_name,
            region=region
        )
        
        if egress_id:
            logger.info(f"LiveKit recording started with ID: {egress_id}")
            logger.info(f"Recording path: {recording_path}")
            
            # Register shutdown callback to stop recording and upload transcript
            async def stop_recording_callback():
                try:
                    success = await stop_livekit_recording(egress_id)
                    if success:
                        logger.info(f"LiveKit recording stopped for session {session_id}")
                    else:
                        logger.error(f"Failed to stop LiveKit recording for session {session_id}")
                except Exception as e:
                    logger.error(f"Error stopping LiveKit recording: {e}")
            
            # Add recording stop callback - but DON'T handle transcript upload here
            ctx.add_shutdown_callback(stop_recording_callback)
        else:
            logger.error("Failed to start LiveKit recording")
    except Exception as e:
        logger.error(f"Error setting up LiveKit recording: {e}")
        logger.error(traceback.format_exc())

    def _calculate_duration_safely(room_config):
        """Safely calculate session duration handling timezone issues."""
        try:
            if not room_config or 'created_at' not in room_config:
                return 0
            
            session_start_str = room_config['created_at']
            session_start = datetime.fromisoformat(session_start_str.replace('Z', '+00:00'))
            
            from datetime import timezone
            current_time = datetime.now(timezone.utc)
            
            duration = (current_time - session_start).total_seconds() / 60
            return max(0, duration)
            
        except Exception as e:
            logger.error(f"Error calculating session duration: {e}")
            return 0
    
    # Enhanced periodic save with better error handling and throttling
    last_save_time = 0
    async def save_transcript_to_file():
        nonlocal last_save_time
        current_time = time.time()
        
        if current_time - last_save_time < 1.0:
            return
            
        try:
            logger.info(f"Saving transcript for user {user_name} ({user_email})...")
            logger.info(f"Found {len(GLOBAL_TRANSCRIPT)} transcript entries in global storage")
            
            # Calculate session duration from start time
            session_start_time = None
            for entry in GLOBAL_TRANSCRIPT:
                if entry.get("timestamp"):
                    try:
                        entry_time = datetime.fromisoformat(entry.get("timestamp"))
                        if session_start_time is None or entry_time < session_start_time:
                            session_start_time = entry_time
                    except:
                        pass
                        
            # Calculate elapsed minutes more reliably
            elapsed_minutes = 0
            if session_start_time:
                elapsed_minutes = (datetime.now() - session_start_time).total_seconds() / 60
                
            # Enhanced transcript data with user info, room config and accurate duration
            transcript_data = {
                "room_name": ctx.room.name,
                "session_id": session_id,  # Use the extracted session_id
                "timestamp": timestamp,
                "last_updated": datetime.now().isoformat(),
                "room_metadata": room_config,
                "user_metadata": user_metadata or {},
                "transcript": GLOBAL_TRANSCRIPT,
                "session_summary": {
                    "user_name": user_name,
                    "user_email": user_email,
                    "interview_id": interview_id,
                    "category": room_config.get('category'),
                    "type": room_config.get('type'),
                    "interview_type": room_config.get('interview_type'),
                    "duration": room_config.get('duration'),
                    "duration_minutes": _calculate_duration_safely(room_config),
                    "elapsed_minutes": elapsed_minutes,  # Store actual elapsed minutes
                    "session_start_time": session_start_time.isoformat() if session_start_time else None,
                    "total_entries": len(GLOBAL_TRANSCRIPT),
                    "session_type": session_type,
                    "is_resumed": room_config.get('session_resumed', False),
                    "recording_info": {
                        "egress_id": egress_id,
                        "bucket": bucket_name,
                        "path": recording_path  # Use the updated path from setup_livekit_recording
                    }
                }
            }
            
            # Save enhanced transcript...
            os.makedirs(os.path.dirname(TRANSCRIPT_FILENAME) or ".", exist_ok=True)
            
            with open(TRANSCRIPT_FILENAME, 'w', encoding='utf-8') as f:
                json.dump(transcript_data, f, ensure_ascii=False, indent=2)
            logger.info(f"Enhanced transcript saved for {user_name} ({user_email}) with {len(GLOBAL_TRANSCRIPT)} entries [{session_type}]")
            logger.info(f"Saved elapsed_minutes: {elapsed_minutes:.1f}")
            
            # Save enhanced text version
            with open(TEXT_TRANSCRIPT_FILENAME, 'w', encoding='utf-8') as f:
                f.write(f"LPDP Interview Transcript\n")
                f.write(f"Session Type: {session_type}\n")
                f.write(f"User: {user_name}\n")
                f.write(f"Email: {user_email}\n")
                f.write(f"Interview ID: {interview_id}\n")
                f.write(f"Interview Type: {room_config.get('interview_type', 'N/A')}\n")
                f.write(f"Category: {room_config.get('category_name', 'N/A')}\n")
                f.write(f"Duration: {room_config.get('duration', 'N/A')}\n")
                f.write(f"Elapsed Minutes: {elapsed_minutes:.1f}\n")  # Add elapsed minutes to text file
                f.write(f"Date: {timestamp}\n")
                f.write(f"Last Updated: {datetime.now().isoformat()}\n")
                f.write(f"Room: {ctx.room.name}\n")
                f.write(f"Total Entries: {len(GLOBAL_TRANSCRIPT)}\n")
                f.write(f"Recording: {recording_path if recording_path else 'Not available'}\n\n")
                
                for entry in GLOBAL_TRANSCRIPT:
                    role = entry.get("role", "unknown")
                    content = entry.get("content", "")
                    entry_time = entry.get("timestamp", "")
                    f.write(f"[{role.upper()}] ({entry_time}):\n{content}\n\n")
            
            logger.info(f"Enhanced text transcript saved for {user_name} ({user_email}) [{session_type}]")
            last_save_time = current_time
            
            # Upload transcript to GCS each time it's saved
            if egress_id:  # Only upload if recording is active
                asyncio.create_task(upload_transcript_to_gcs(
                    session_id=session_id,  # Use the extracted session_id
                    transcript_file_path=TRANSCRIPT_FILENAME,
                    bucket_name=bucket_name
                ))
            
        except Exception as e:
            logger.error(f"Error saving transcript for {user_name} ({user_email}): {e}")
            logger.error(traceback.format_exc())

    async def final_transcript_upload():
        try:
            # Do one final save to ensure latest data is captured
            await save_transcript_to_file()
            logger.info(f"Final transcript save completed for session {session_id}")
            
            # Always upload transcript to GCS at the end, regardless of recording status
            if TRANSCRIPT_FILENAME and os.path.exists(TRANSCRIPT_FILENAME):
                upload_success = await upload_transcript_to_gcs(
                    session_id=session_id,  # Use the extracted session_id
                    transcript_file_path=TRANSCRIPT_FILENAME,
                    bucket_name=bucket_name
                )
                
                if upload_success:
                    logger.info(f"FINAL UPLOAD: Successfully uploaded transcript to GCS bucket for session {session_id}")
                else:
                    logger.error(f"FINAL UPLOAD: Failed to upload transcript to GCS bucket for session {session_id}")
                    
                # Try one more time if failed (for extra reliability)
                if not upload_success:
                    logger.info("Retrying final transcript upload...")
                    # Wait a moment before retry
                    await asyncio.sleep(2)
                    retry_success = await upload_transcript_to_gcs(
                        session_id=session_id,  # Use the extracted session_id
                        transcript_file_path=TRANSCRIPT_FILENAME,
                        bucket_name=bucket_name
                    )
                    if retry_success:
                        logger.info(f"RETRY SUCCESSFUL: Uploaded transcript to GCS bucket for session {session_id}")
            else:
                logger.error(f"FINAL UPLOAD: Transcript file not found: {TRANSCRIPT_FILENAME}")
        except Exception as e:
            logger.error(f"Error in final transcript upload: {e}")
            logger.error(traceback.format_exc())
    
    ctx.add_shutdown_callback(final_transcript_upload)

    async def periodic_save():
        nonlocal last_save_time
        while True:
            try:
                await asyncio.sleep(30)
                current_time = time.time()
                if (current_time - last_save_time > 10 and len(GLOBAL_TRANSCRIPT) > 0):
                    logger.info(f"Periodic backup save: {len(GLOBAL_TRANSCRIPT)} entries to save")
                    await save_transcript_to_file()
                else:
                    logger.debug("Periodic save: Skipping (recent save or no content)")
            except asyncio.CancelledError:
                logger.info("Periodic save task cancelled")
                break
            except Exception as e:
                logger.error(f"Error in periodic save: {e}")

    def get_tts_instructions(language='en'):
        if language == 'id':
            return (
                "You are speaking Indonesian content. "
                "Pronounce Indonesian words naturally and clearly. "
                "Use appropriate Indonesian pronunciation for proper nouns and technical terms. "
                "Maintain a professional, warm tone suitable for an academic interview setting."
                "Natural-natural-natural, you need to use indonesian accent more clearly and faster."
            )
        else:
            return (
                "You are conducting a professional academic interview. "
                "Speak clearly and maintain a warm, encouraging tone. "
                "When encountering Indonesian names or terms, pronounce them as naturally as possible."
            )
    
    # Register shutdown callback
    ctx.add_shutdown_callback(save_transcript_to_file)
    
    # Your existing session setup...
    azure_endpoint = os.getenv("AZURE_OPENAI_ENDPOINT")
    azure_api_key = os.getenv("AZURE_OPENAI_API_KEY")
    azure_api_version = os.getenv("AZURE_OPENAI_API_VERSION")
    llm_deployment = os.getenv("AZURE_OPENAI_LLM_DEPLOYMENT")
    stt_deployment = os.getenv("AZURE_OPENAI_STT_DEPLOYMENT")
    tts_deployment = os.getenv("AZURE_OPENAI_TTS_DEPLOYMENT")
    stt_azure_key = os.getenv("AZURE_SPEECH_KEY")
    stt_azure_region = os.getenv("AZURE_SPEECH_REGION")
    custom_azure_endpoint = os.getenv("CUSTOM_AZURE_OPENAI_ENDPOINT")
    custom_azure_api_key = os.getenv("CUSTOM_AZURE_OPENAI_API_KEY")
    
    # Create agent instance with room metadata - this will handle transcript loading for resumed sessions
    try:
        lpdp_agent = LPDPInterviewAgent(room_metadata, user_metadata)
        logger.info("LPDP Agent created successfully")
    except Exception as e:
        logger.error(f"Error creating LPDP Agent: {e}")
        logger.error(traceback.format_exc())
        return
    
    # Set up the save callback for the agent
    lpdp_agent.save_transcript_callback = save_transcript_to_file
    
    # Set STT languages based on selected language
    if lpdp_agent.language == "id":
        stt_languages = ["id-ID"]  # Prioritaskan Bahasa Indonesia
    elif lpdp_agent.language == "en":
        stt_languages = ["en-US", "id-ID", "en-GB"]  # Prioritaskan Bahasa Inggris
    else:
        stt_languages = ["en-US", "id-ID", "en-GB"]  # Bahasa default jika self.language tidak 'id' atau 'en'

    # Correct Azure STT configuration based on actual source code
    try:
        session = AgentSession(
            vad=ctx.proc.userdata["vad"],
            llm=google.LLM(
                model="gemini-2.0-flash-001",
                temperature=0.7,
                max_output_tokens=4000,
                top_p=0.95,
                top_k=40
            ),
            stt=azure.STT(
                speech_key=stt_azure_key,
                speech_region=stt_azure_region,
                language=stt_languages,
                
                # These settings are working well - keep them:
                segmentation_silence_timeout_ms=2000,  # 3 seconds silence before ending segment
                segmentation_max_time_ms=30000,       # Maximum 30 seconds per segment
                segmentation_strategy="Default",
                sample_rate=16000,
                num_channels=1,
            ),
            tts=openai.TTS.with_azure(
                model=tts_deployment,
                azure_endpoint=custom_azure_endpoint,
                api_key=custom_azure_api_key,
                api_version=azure_api_version,
                voice="nova",
                speed=2.0,
                instructions=get_tts_instructions(lpdp_agent.language),
            ),
            
            # OPTION 1: Use VAD-only (more reliable, no timeouts)
            turn_detection="vad",  # This avoids the MultilingualModel timeout issues
            
            # Keep these settings - they're working well:
            min_endpointing_delay=2.0,    # Wait 3 seconds after speech ends
            max_endpointing_delay=10.0,   # Maximum wait time
            
            # Interruption controls
            allow_interruptions=True,
            min_interruption_duration=1.0,
            min_interruption_words=3,
        )
        logger.info("Agent session created with VAD-only turn detection (more reliable)")

    except Exception as e:
        logger.error(f"Error creating agent session: {e}")
        logger.error(traceback.format_exc())
        return
    
    @session.on("conversation_item_added")
    def on_conversation_item_added(event):
        """Handle both user and agent conversation items"""
        global GLOBAL_TRANSCRIPT
        try:
            # Skip if we're in resume mode
            if (hasattr(lpdp_agent, 'session_state') and 
                lpdp_agent.session_state.get('resume_in_progress', False)):
                logger.info("Skipping transcript entry during resume setup")
                return
            
            # Determine role based on the event item
            role = "applicant" if event.item.role == "user" else "interviewer"
            
            # Get text content from the item
            text_content = event.item.text_content if hasattr(event.item, 'text_content') else str(event.item)
            
            # SIMPLE FIX: Skip if content is empty or just whitespace
            if not text_content or not text_content.strip():
                logger.info(f"Skipping empty {role} content")
                return
            
            entry = {
                "role": role,
                "content": text_content,
                "timestamp": datetime.now().isoformat(),
                "source": "conversation_item",
                "interrupted": getattr(event.item, 'interrupted', False)
            }
            
            GLOBAL_TRANSCRIPT.append(entry)
            session_type = "RESUMED" if room_config.get('session_resumed') else "NEW"
            logger.info(f"{role} speech from {user_name} ({user_email}) [{session_type}]: {text_content}")
            logger.info(f"Transcript now has {len(GLOBAL_TRANSCRIPT)} entries (conversation_item_added)")
            
            # Trigger immediate save
            asyncio.create_task(save_transcript_to_file())
            
        except Exception as e:
            logger.error(f"Error in conversation_item_added handler: {e}")
    
    # Start the session with enhanced agent
    save_task = None
    try:
        await session.start(
            agent=lpdp_agent,
            room=ctx.room,
            room_input_options=RoomInputOptions(
                noise_cancellation=noise_cancellation.BVC()
            ),
            room_output_options=RoomOutputOptions(transcription_enabled=True),
        )
        session_type = "RESUMED" if room_config.get('session_resumed') else "NEW"
        logger.info(f"{session_type} Session started for user {user_name} ({user_email}) with enhanced agent")
        
        # Start periodic save task AFTER session starts successfully
        save_task = asyncio.create_task(periodic_save())
        logger.info("Started periodic transcript saving task")
        
    except Exception as e:
        logger.error(f"Error starting session for user {user_name} ({user_email}): {e}")
        logger.error(traceback.format_exc())
    finally:
        # Cancel periodic save task when session ends
        if save_task and not save_task.done():
            logger.info("Cancelling periodic save task...")
            save_task.cancel()
            try:
                await save_task
            except asyncio.CancelledError:
                logger.info("Periodic save task cancelled successfully")
                pass
        
        # Final save when session ends - but don't upload here, let the shutdown callback handle it
        try:
            logger.info(f"Performing final transcript save with {len(GLOBAL_TRANSCRIPT)} entries...")
            await save_transcript_to_file()
            logger.info("Final transcript save completed in finally block")
        except Exception as e:
            logger.error(f"Error in final save: {e}")


if __name__ == "__main__":
    cli.run_app(WorkerOptions(
        entrypoint_fnc=entrypoint, 
        prewarm_fnc=prewarm,
        shutdown_process_timeout=120
    ))